<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Services\WooCommerceService;

class WishlistController extends Controller
{
    protected $wooCommerceService;

    public function __construct(WooCommerceService $wooCommerceService)
    {
        $this->wooCommerceService = $wooCommerceService;
    }

    public function index()
    {
        $wishlist = Session::get('wishlist', []);
        $wishlistItems = [];

        foreach ($wishlist as $productId) {
            try {
                $product = $this->wooCommerceService->getProduct($productId);
                if ($product) {
                    $wishlistItems[] = $product;
                }
            } catch (\Exception $e) {
                // Skip products that can't be loaded
                continue;
            }
        }

        return view('wishlist.index', compact('wishlistItems'));
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer'
        ]);

        $wishlist = Session::get('wishlist', []);
        $productId = $request->product_id;

        if (!in_array($productId, $wishlist)) {
            $wishlist[] = $productId;
            Session::put('wishlist', $wishlist);
            $message = 'Product added to wishlist!';
        } else {
            $message = 'Product is already in your wishlist.';
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'wishlist_count' => count($wishlist),
                'in_wishlist' => true
            ]);
        }

        return redirect()->back()->with('success', $message);
    }

    public function remove(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer'
        ]);

        $wishlist = Session::get('wishlist', []);
        $productId = $request->product_id;

        $wishlist = array_filter($wishlist, function($id) use ($productId) {
            return $id != $productId;
        });

        Session::put('wishlist', array_values($wishlist));

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Product removed from wishlist',
                'wishlist_count' => count($wishlist),
                'in_wishlist' => false
            ]);
        }

        return redirect()->back()->with('success', 'Product removed from wishlist!');
    }

    public function clear()
    {
        Session::forget('wishlist');

        return response()->json([
            'success' => true,
            'message' => 'Wishlist cleared'
        ]);
    }

    public function count()
    {
        $wishlist = Session::get('wishlist', []);
        return response()->json([
            'count' => count($wishlist)
        ]);
    }

    public function check(Request $request)
    {
        $request->validate([
            'product_id' => 'required|integer'
        ]);

        $wishlist = Session::get('wishlist', []);
        $inWishlist = in_array($request->product_id, $wishlist);

        return response()->json([
            'in_wishlist' => $inWishlist
        ]);
    }
}
