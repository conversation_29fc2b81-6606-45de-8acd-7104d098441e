<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class WooCommerceFixController extends Controller
{
    public function diagnoseAPI()
    {
        $storeUrl = env('WOOCOMMERCE_STORE_URL');
        $consumerKey = env('WOOCOMMERCE_CONSUMER_KEY');
        $consumerSecret = env('WOOCOMMERCE_CONSUMER_SECRET');

        $results = [
            'store_url' => $storeUrl,
            'api_key_format' => [
                'consumer_key_length' => strlen($consumerKey),
                'consumer_secret_length' => strlen($consumerSecret),
                'key_starts_with' => substr($consumerKey, 0, 3),
                'secret_starts_with' => substr($consumerSecret, 0, 3),
            ]
        ];

        // Test 1: Check if store is accessible
        try {
            $storeResponse = Http::timeout(10)->get($storeUrl);
            $results['store_accessibility'] = [
                'accessible' => $storeResponse->successful(),
                'status_code' => $storeResponse->status(),
                'is_wordpress' => str_contains($storeResponse->body(), 'wp-content') || str_contains($storeResponse->body(), 'WordPress'),
                'has_woocommerce' => str_contains($storeResponse->body(), 'woocommerce') || str_contains($storeResponse->body(), 'WooCommerce'),
            ];
        } catch (\Exception $e) {
            $results['store_accessibility'] = ['error' => $e->getMessage()];
        }

        // Test 2: Check WooCommerce REST API endpoint
        try {
            $apiBaseUrl = $storeUrl . '/wp-json/wc/v3';
            $apiResponse = Http::timeout(10)->get($apiBaseUrl);
            $results['woocommerce_api_base'] = [
                'accessible' => $apiResponse->successful(),
                'status_code' => $apiResponse->status(),
                'response' => $apiResponse->json(),
            ];
        } catch (\Exception $e) {
            $results['woocommerce_api_base'] = ['error' => $e->getMessage()];
        }

        // Test 3: Test different API endpoints with current credentials
        $endpoints = [
            'system_status' => '/system_status',
            'data' => '/data',
            'products' => '/products?per_page=1',
            'orders' => '/orders?per_page=1',
            'customers' => '/customers?per_page=1',
        ];

        foreach ($endpoints as $name => $endpoint) {
            try {
                $url = $storeUrl . '/wp-json/wc/v3' . $endpoint;
                $response = Http::timeout(30)
                    ->withBasicAuth($consumerKey, $consumerSecret)
                    ->get($url);

                $results['endpoint_tests'][$name] = [
                    'url' => $url,
                    'status_code' => $response->status(),
                    'successful' => $response->successful(),
                    'response' => $response->json(),
                    'headers' => $response->headers(),
                ];
            } catch (\Exception $e) {
                $results['endpoint_tests'][$name] = [
                    'url' => $url ?? 'N/A',
                    'error' => $e->getMessage()
                ];
            }
        }

        // Test 4: Try with different authentication methods
        try {
            $url = $storeUrl . '/wp-json/wc/v3/products?per_page=1';
            
            // Method 1: Basic Auth (current method)
            $basicAuthResponse = Http::timeout(30)
                ->withBasicAuth($consumerKey, $consumerSecret)
                ->get($url);

            $results['auth_methods']['basic_auth'] = [
                'status_code' => $basicAuthResponse->status(),
                'successful' => $basicAuthResponse->successful(),
                'response' => $basicAuthResponse->json(),
            ];

            // Method 2: Query parameters
            $queryAuthResponse = Http::timeout(30)
                ->get($url, [
                    'consumer_key' => $consumerKey,
                    'consumer_secret' => $consumerSecret,
                ]);

            $results['auth_methods']['query_params'] = [
                'status_code' => $queryAuthResponse->status(),
                'successful' => $queryAuthResponse->successful(),
                'response' => $queryAuthResponse->json(),
            ];

        } catch (\Exception $e) {
            $results['auth_methods'] = ['error' => $e->getMessage()];
        }

        return response()->json($results, 200, [], JSON_PRETTY_PRINT);
    }

    public function generateNewKeys()
    {
        $storeUrl = env('WOOCOMMERCE_STORE_URL');
        
        return view('admin.generate-keys', [
            'store_url' => $storeUrl,
            'admin_url' => $storeUrl . '/wp-admin/admin.php?page=wc-settings&tab=advanced&section=keys',
            'current_key' => env('WOOCOMMERCE_CONSUMER_KEY'),
            'current_secret' => env('WOOCOMMERCE_CONSUMER_SECRET'),
        ]);
    }

    public function testNewKeys(Request $request)
    {
        $request->validate([
            'consumer_key' => 'required|string',
            'consumer_secret' => 'required|string',
        ]);

        $storeUrl = env('WOOCOMMERCE_STORE_URL');
        $newKey = $request->consumer_key;
        $newSecret = $request->consumer_secret;

        try {
            $url = $storeUrl . '/wp-json/wc/v3/products?per_page=1';
            
            $response = Http::timeout(30)
                ->withBasicAuth($newKey, $newSecret)
                ->get($url);

            if ($response->successful()) {
                return response()->json([
                    'success' => true,
                    'message' => 'New API keys work perfectly!',
                    'status_code' => $response->status(),
                    'data' => $response->json(),
                    'next_steps' => [
                        'Update your .env file with these new keys',
                        'Run: php artisan config:clear',
                        'Test the application again'
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'New API keys still have issues',
                    'status_code' => $response->status(),
                    'error' => $response->json(),
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error testing new keys',
                'error' => $e->getMessage(),
            ]);
        }
    }
}
