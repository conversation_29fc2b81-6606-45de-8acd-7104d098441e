@extends('layouts.app')

@section('title', 'PayPal Payment')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-md p-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">PayPal Checkout</h1>
            
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h2 class="text-lg font-semibold text-blue-900 mb-2">Order Summary</h2>
                <p class="text-blue-800">Total: €{{ number_format($total, 2) }}</p>
            </div>
            
            <div class="mb-6">
                <p class="text-gray-600 mb-4">
                    You will be redirected to PayPal to complete your payment securely.
                </p>
                
                <!-- PayPal Button Container -->
                <div id="paypal-button-container" class="mb-4"></div>
                
                <!-- Demo PayPal Button -->
                <button type="button" id="demo-paypal-button" 
                        class="w-full bg-yellow-500 text-black py-3 px-4 rounded-md hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 font-medium">
                    Pay with PayPal (Demo)
                </button>
            </div>
            
            <div class="mt-6 text-center">
                <a href="{{ route('cart.checkout') }}" class="text-gray-600 hover:text-gray-800">
                    ← Back to Checkout
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Demo PayPal integration
    document.getElementById('demo-paypal-button').addEventListener('click', function() {
        // Simulate PayPal processing
        this.disabled = true;
        this.textContent = 'Processing...';
        
        // Simulate API call delay
        setTimeout(() => {
            // Create a hidden form to submit the demo data
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("paypal.process") }}';
            
            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);
            
            // Add demo PayPal order ID
            const paypalOrderId = document.createElement('input');
            paypalOrderId.type = 'hidden';
            paypalOrderId.name = 'paypal_order_id';
            paypalOrderId.value = 'PAYPAL_' + Math.random().toString(36).substr(2, 9);
            form.appendChild(paypalOrderId);
            
            // Add demo payer ID
            const payerId = document.createElement('input');
            payerId.type = 'hidden';
            payerId.name = 'payer_id';
            payerId.value = 'PAYER_' + Math.random().toString(36).substr(2, 9);
            form.appendChild(payerId);
            
            document.body.appendChild(form);
            form.submit();
        }, 2000);
    });
</script>
@endsection
