<div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 group border border-gray-100">
    <div class="relative overflow-hidden">
        <!-- Product Image -->
        <a href="<?php echo e(route('product.show', $product['id'])); ?>" class="block">
            <div class="aspect-square bg-gray-100 relative">
                <img src="<?php echo e($product['image']); ?>"
                     alt="<?php echo e($product['name']); ?>"
                     class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                     loading="lazy"
                     onerror="this.src='/placeholder.jpg'">

                <!-- Image Overlay on Hover -->
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
            </div>
        </a>
        
        <!-- Sale Badge -->
        <?php if(isset($product['sale_price']) && $product['sale_price'] && $product['sale_price'] < $product['regular_price']): ?>
            <div class="absolute top-3 left-3 z-10">
                <span class="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    SALE
                </span>
            </div>
        <?php endif; ?>

        <!-- Featured Badge -->
        <?php if(isset($product['featured']) && $product['featured']): ?>
            <div class="absolute top-3 right-3 z-10">
                <span class="bg-gradient-to-r from-yellow-400 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    ⭐ Featured
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Quick Actions -->
        <div class="absolute bottom-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <!-- Quick View Button -->
            <button onclick="quickView(<?php echo e($product['id']); ?>)"
                    class="bg-white hover:bg-gray-50 text-gray-700 p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
                    title="Quick View">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </button>

            <!-- Add to Cart Button -->
            <button onclick="addToCart(<?php echo e($product['id']); ?>)"
                    class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
                    title="Add to Cart">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4.5M9 7h6"></path>
                </svg>
            </button>
        </div>
    </div>
    
    <div class="p-5">
        <!-- Product Category -->
        <?php if(isset($product['category']) && $product['category']): ?>
            <div class="text-xs text-blue-600 uppercase tracking-wide mb-2 font-semibold">
                <?php echo e($product['category']); ?>

            </div>
        <?php endif; ?>

        <!-- Product Name -->
        <h3 class="text-sm font-semibold text-gray-900 mb-3 line-clamp-2 leading-tight">
            <a href="<?php echo e(route('product.show', $product['id'])); ?>" class="hover:text-blue-600 transition-colors">
                <?php echo e($product['name']); ?>

            </a>
        </h3>
        
        <!-- Product Rating -->
        <?php if(isset($product['rating']) && $product['rating'] > 0): ?>
            <div class="flex items-center mb-2">
                <div class="flex items-center">
                    <?php for($i = 1; $i <= 5; $i++): ?>
                        <?php if($i <= floor($product['rating'])): ?>
                            <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        <?php elseif($i == ceil($product['rating']) && $product['rating'] - floor($product['rating']) >= 0.5): ?>
                            <svg class="w-4 h-4 text-yellow-400" viewBox="0 0 20 20">
                                <defs>
                                    <linearGradient id="half-fill-<?php echo e($product['id']); ?>">
                                        <stop offset="50%" stop-color="currentColor"/>
                                        <stop offset="50%" stop-color="transparent"/>
                                    </linearGradient>
                                </defs>
                                <path fill="url(#half-fill-<?php echo e($product['id']); ?>)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        <?php else: ?>
                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>
                <span class="text-xs text-gray-500 ml-1">
                    (<?php echo e($product['review_count'] ?? 0); ?>)
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Product Price -->
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <?php if(isset($product['sale_price']) && $product['sale_price'] && $product['sale_price'] < $product['regular_price']): ?>
                    <span class="text-lg font-bold text-red-600">
                        $<?php echo e(number_format($product['sale_price'], 2)); ?>

                    </span>
                    <span class="text-sm text-gray-500 line-through">
                        $<?php echo e(number_format($product['regular_price'], 2)); ?>

                    </span>
                <?php else: ?>
                    <span class="text-lg font-bold text-gray-900">
                        $<?php echo e(number_format($product['price'] ?? $product['regular_price'] ?? 0, 2)); ?>

                    </span>
                <?php endif; ?>
            </div>
            
            <!-- Stock Status -->
            <?php if(isset($product['in_stock'])): ?>
                <?php if($product['in_stock']): ?>
                    <span class="text-xs text-green-600 font-medium">In Stock</span>
                <?php else: ?>
                    <span class="text-xs text-red-600 font-medium">Out of Stock</span>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Add to Cart Button -->
        <div class="mt-4">
            <?php if(isset($product['in_stock']) && $product['in_stock']): ?>
                <button onclick="addToCart(<?php echo e($product['id']); ?>)"
                        class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2.5 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg">
                    Add to Cart
                </button>
            <?php else: ?>
                <button disabled
                        class="w-full bg-gray-300 text-gray-500 font-medium py-2.5 px-4 rounded-lg cursor-not-allowed">
                    Out of Stock
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.aspect-square {
    aspect-ratio: 1 / 1;
}
</style>

<script>
function addToCart(productId) {
    // Add loading state
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<svg class="animate-spin h-4 w-4 mx-auto" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
    button.disabled = true;

    // Simulate API call (replace with actual cart functionality)
    setTimeout(() => {
        button.innerHTML = '✓ Added';
        button.classList.add('bg-green-600', 'hover:bg-green-700');
        button.classList.remove('bg-blue-600', 'hover:bg-blue-700', 'from-blue-600', 'to-blue-700', 'hover:from-blue-700', 'hover:to-blue-800');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            button.classList.remove('bg-green-600', 'hover:bg-green-700');
            button.classList.add('bg-gradient-to-r', 'from-blue-600', 'to-blue-700', 'hover:from-blue-700', 'hover:to-blue-800');
        }, 2000);
    }, 1000);
}

function quickView(productId) {
    // Add quick view functionality
    window.location.href = `/shop/product/${productId}`;
}
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/components/product-card.blade.php ENDPATH**/ ?>