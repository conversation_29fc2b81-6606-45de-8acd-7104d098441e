<div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 group">
    <div class="relative">
        <!-- Product Image -->
        <a href="<?php echo e(route('product.show', $product['id'])); ?>" class="block">
            <img src="<?php echo e($product['image']); ?>" 
                 alt="<?php echo e($product['name']); ?>" 
                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                 loading="lazy">
        </a>
        
        <!-- Sale Badge -->
        <?php if(isset($product['sale_price']) && $product['sale_price'] && $product['sale_price'] < $product['regular_price']): ?>
            <div class="absolute top-2 left-2">
                <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    SALE
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Featured Badge -->
        <?php if(isset($product['featured']) && $product['featured']): ?>
            <div class="absolute top-2 right-2">
                <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    ⭐ Featured
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Quick Add to Cart Button -->
        <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button onclick="addToCart(<?php echo e($product['id']); ?>)" 
                    class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4.5M9 7h6"></path>
                </svg>
            </button>
        </div>
    </div>
    
    <div class="p-4">
        <!-- Product Category -->
        <?php if(isset($product['category']) && $product['category']): ?>
            <div class="text-xs text-gray-500 uppercase tracking-wide mb-1">
                <?php echo e($product['category']); ?>

            </div>
        <?php endif; ?>
        
        <!-- Product Name -->
        <h3 class="text-sm font-medium text-gray-900 mb-2 line-clamp-2">
            <a href="<?php echo e(route('product.show', $product['id'])); ?>" class="hover:text-blue-600 transition-colors">
                <?php echo e($product['name']); ?>

            </a>
        </h3>
        
        <!-- Product Rating -->
        <?php if(isset($product['rating']) && $product['rating'] > 0): ?>
            <div class="flex items-center mb-2">
                <div class="flex items-center">
                    <?php for($i = 1; $i <= 5; $i++): ?>
                        <?php if($i <= floor($product['rating'])): ?>
                            <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        <?php elseif($i == ceil($product['rating']) && $product['rating'] - floor($product['rating']) >= 0.5): ?>
                            <svg class="w-4 h-4 text-yellow-400" viewBox="0 0 20 20">
                                <defs>
                                    <linearGradient id="half-fill-<?php echo e($product['id']); ?>">
                                        <stop offset="50%" stop-color="currentColor"/>
                                        <stop offset="50%" stop-color="transparent"/>
                                    </linearGradient>
                                </defs>
                                <path fill="url(#half-fill-<?php echo e($product['id']); ?>)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        <?php else: ?>
                            <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>
                <span class="text-xs text-gray-500 ml-1">
                    (<?php echo e($product['review_count'] ?? 0); ?>)
                </span>
            </div>
        <?php endif; ?>
        
        <!-- Product Price -->
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <?php if(isset($product['sale_price']) && $product['sale_price'] && $product['sale_price'] < $product['regular_price']): ?>
                    <span class="text-lg font-bold text-red-600">
                        $<?php echo e(number_format($product['sale_price'], 2)); ?>

                    </span>
                    <span class="text-sm text-gray-500 line-through">
                        $<?php echo e(number_format($product['regular_price'], 2)); ?>

                    </span>
                <?php else: ?>
                    <span class="text-lg font-bold text-gray-900">
                        $<?php echo e(number_format($product['price'] ?? $product['regular_price'] ?? 0, 2)); ?>

                    </span>
                <?php endif; ?>
            </div>
            
            <!-- Stock Status -->
            <?php if(isset($product['in_stock'])): ?>
                <?php if($product['in_stock']): ?>
                    <span class="text-xs text-green-600 font-medium">In Stock</span>
                <?php else: ?>
                    <span class="text-xs text-red-600 font-medium">Out of Stock</span>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- Add to Cart Button -->
        <div class="mt-4">
            <?php if(isset($product['in_stock']) && $product['in_stock']): ?>
                <button onclick="addToCart(<?php echo e($product['id']); ?>)" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                    Add to Cart
                </button>
            <?php else: ?>
                <button disabled 
                        class="w-full bg-gray-300 text-gray-500 font-medium py-2 px-4 rounded-lg cursor-not-allowed">
                    Out of Stock
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/components/product-card.blade.php ENDPATH**/ ?>