<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - Deal4u</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- Categories Header -->
    <section class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
            <div class="text-center">
                <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-4">
                    <span class="text-xs font-semibold tracking-wider">🛍️ SHOP BY CATEGORY 🛍️</span>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    Product Categories
                </h1>
                <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                    Browse our wide range of premium gaming consoles, electronics, and tech products
                </p>
            </div>
        </div>
    </section>

    <div class="min-h-screen bg-gray-50 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Categories Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                <!-- Electronics Category -->
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="relative overflow-hidden">
                        <div class="w-full h-56 bg-gradient-to-br from-blue-500 via-purple-500 to-blue-600 flex items-center justify-center relative overflow-hidden">
                            <!-- Background pattern -->
                            <div class="absolute inset-0 opacity-20">
                                <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
                            </div>
                            <div class="relative z-10 text-center">
                                <span class="text-white text-4xl font-bold mb-2 block">📱</span>
                                <div class="text-white text-xs opacity-75">Electronics</div>
                            </div>
                        </div>
                        
                        <div class="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                            60 products
                        </div>
                        
                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">Electronics</h3>
                        
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Latest smartphones, tablets, laptops and electronic gadgets
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                60 products
                            </div>
                            
                            <a href="#" 
                               class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-5 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Gaming Consoles Category -->
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="relative overflow-hidden">
                        <div class="w-full h-56 bg-gradient-to-br from-green-500 via-blue-500 to-purple-600 flex items-center justify-center relative overflow-hidden">
                            <!-- Background pattern -->
                            <div class="absolute inset-0 opacity-20">
                                <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
                            </div>
                            <div class="relative z-10 text-center">
                                <span class="text-white text-4xl font-bold mb-2 block">🎮</span>
                                <div class="text-white text-xs opacity-75">Gaming Consoles</div>
                            </div>
                        </div>
                        
                        <div class="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                            60 products
                        </div>
                        
                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">Video Game Consoles</h3>
                        
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Latest gaming consoles, handheld devices and gaming accessories
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                60 products
                            </div>
                            
                            <a href="#" 
                               class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-5 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Smart Watches Category -->
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="relative overflow-hidden">
                        <div class="w-full h-56 bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 flex items-center justify-center relative overflow-hidden">
                            <!-- Background pattern -->
                            <div class="absolute inset-0 opacity-20">
                                <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
                            </div>
                            <div class="relative z-10 text-center">
                                <span class="text-white text-4xl font-bold mb-2 block">⌚</span>
                                <div class="text-white text-xs opacity-75">Smart Watches</div>
                            </div>
                        </div>
                        
                        <div class="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                            25 products
                        </div>
                        
                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">Smart Watches</h3>
                        
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Latest smartwatches and fitness trackers from top brands
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                25 products
                            </div>
                            
                            <a href="#" 
                               class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-5 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Accessories Category -->
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="relative overflow-hidden">
                        <div class="w-full h-56 bg-gradient-to-br from-teal-500 via-cyan-500 to-blue-600 flex items-center justify-center relative overflow-hidden">
                            <!-- Background pattern -->
                            <div class="absolute inset-0 opacity-20">
                                <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
                            </div>
                            <div class="relative z-10 text-center">
                                <span class="text-white text-4xl font-bold mb-2 block">🔌</span>
                                <div class="text-white text-xs opacity-75">Accessories</div>
                            </div>
                        </div>
                        
                        <div class="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                            15 products
                        </div>
                        
                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">Accessories</h3>
                        
                        <p class="text-gray-600 text-sm mb-4 leading-relaxed">
                            Cables, chargers, cases and other tech accessories
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                15 products
                            </div>
                            
                            <a href="#" 
                               class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-5 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success message -->
            <div class="mt-12 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                <strong>✅ Categories Design Updated!</strong> The categories page now matches the "New folder (2)" design with modern gradients, animations, and styling.
            </div>
        </div>
    </div>
</body>
</html>
