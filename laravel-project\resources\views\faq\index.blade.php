@extends('layouts.app')

@section('title', 'Frequently Asked Questions')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-gray-900">Frequently Asked Questions</h1>
            <p class="text-gray-600 mt-2">Find answers to common questions about our products and services</p>
        </div>

        <!-- Search FAQ -->
        <div class="mb-8">
            <div class="relative">
                <input type="text" id="faq-search" placeholder="Search FAQs..." 
                       class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- FAQ Categories -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-2">
                <button onclick="filterFAQ('all')" class="faq-filter-btn active bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                    All
                </button>
                <button onclick="filterFAQ('orders')" class="faq-filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">
                    Orders
                </button>
                <button onclick="filterFAQ('shipping')" class="faq-filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">
                    Shipping
                </button>
                <button onclick="filterFAQ('returns')" class="faq-filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">
                    Returns
                </button>
                <button onclick="filterFAQ('payments')" class="faq-filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">
                    Payments
                </button>
                <button onclick="filterFAQ('account')" class="faq-filter-btn bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">
                    Account
                </button>
            </div>
        </div>

        <!-- FAQ Items -->
        <div class="space-y-4" id="faq-container">
            <!-- Orders -->
            <div class="faq-item bg-white rounded-lg shadow-md" data-category="orders">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">How do I track my order?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">Once your order ships, you'll receive a tracking number via email. You can also track your order by logging into your account and viewing your order history.</p>
                </div>
            </div>

            <div class="faq-item bg-white rounded-lg shadow-md" data-category="orders">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Can I modify or cancel my order?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">You can modify or cancel your order within 1 hour of placing it. After that, please contact our customer service team for assistance.</p>
                </div>
            </div>

            <!-- Shipping -->
            <div class="faq-item bg-white rounded-lg shadow-md" data-category="shipping">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">What are your shipping options?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">We offer standard shipping (3-5 business days) and express shipping (1-2 business days). Free shipping is available on orders over €50.</p>
                </div>
            </div>

            <div class="faq-item bg-white rounded-lg shadow-md" data-category="shipping">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Do you ship internationally?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">Yes, we ship to most countries worldwide. International shipping costs and delivery times vary by destination. Additional customs fees may apply.</p>
                </div>
            </div>

            <!-- Returns -->
            <div class="faq-item bg-white rounded-lg shadow-md" data-category="returns">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">What is your return policy?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">We offer a 30-day return policy for most items. Items must be in original condition with tags attached. Some restrictions apply to certain product categories.</p>
                </div>
            </div>

            <!-- Payments -->
            <div class="faq-item bg-white rounded-lg shadow-md" data-category="payments">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">What payment methods do you accept?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">We accept all major credit cards, PayPal, Apple Pay, Google Pay, bank transfers, and cash on delivery (where available).</p>
                </div>
            </div>

            <!-- Account -->
            <div class="faq-item bg-white rounded-lg shadow-md" data-category="account">
                <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">How do I create an account?</h3>
                        <svg class="faq-icon h-5 w-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </button>
                <div class="faq-answer hidden px-6 pb-6">
                    <p class="text-gray-600">Click the "Register" link in the top navigation, fill out the required information, and verify your email address. You can also create an account during checkout.</p>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-8 text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Still have questions?</h2>
            <p class="text-gray-600 mb-6">Can't find the answer you're looking for? Our customer support team is here to help.</p>
            <a href="{{ route('contact') }}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Contact Support
            </a>
        </div>
    </div>
</div>

<script>
function toggleFAQ(button) {
    const answer = button.nextElementSibling;
    const icon = button.querySelector('.faq-icon');
    
    if (answer.classList.contains('hidden')) {
        answer.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
    } else {
        answer.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
}

function filterFAQ(category) {
    const items = document.querySelectorAll('.faq-item');
    const buttons = document.querySelectorAll('.faq-filter-btn');
    
    // Update button styles
    buttons.forEach(btn => {
        btn.classList.remove('active', 'bg-blue-600', 'text-white');
        btn.classList.add('bg-gray-200', 'text-gray-700');
    });
    
    event.target.classList.remove('bg-gray-200', 'text-gray-700');
    event.target.classList.add('active', 'bg-blue-600', 'text-white');
    
    // Filter items
    items.forEach(item => {
        if (category === 'all' || item.dataset.category === category) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

// Search functionality
document.getElementById('faq-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const items = document.querySelectorAll('.faq-item');
    
    items.forEach(item => {
        const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
        const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
        
        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});
</script>
@endsection
