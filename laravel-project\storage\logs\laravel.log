[2025-06-28 19:21:10] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_1f3...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(24): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:10] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":8,"page":1,"status":"publish","featured":true}} 
[2025-06-28 19:21:12] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_1f3...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(24): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:12] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:21:12] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_69d...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(31): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:12] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","orderby":"popularity","order":"desc"}} 
[2025-06-28 19:21:15] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_69d...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(31): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:15] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:21:16] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('Error fetching ...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(467): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_categories', 3600, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(430): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(39): App\\Services\\WooCommerceService->getCategories()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:16] laravel.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:21:16] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_a72...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(42): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:16] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":6,"page":1,"status":"publish","orderby":"date","order":"desc"}} 
[2025-06-28 19:21:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_a72...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(42): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:18] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:21:18] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_50c...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(50): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:18] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","on_sale":true}} 
[2025-06-28 19:21:19] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_50c...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(50): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:21:19] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:21:20] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(317): Illuminate\\Log\\LogManager->error('Unable to resol...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Spatie\\LaravelIgnition\\Exceptions\\ViewException))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(49): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Spatie\\LaravelIgnition\\Exceptions\\ViewException))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\ViewException))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#28 {main}
"} 
[2025-06-28 19:21:20] laravel.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2","data":{"featuredProducts":"<pre class=sf-dump id=sf-dump-1938710039 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1938710039\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","bestSellers":"<pre class=sf-dump id=sf-dump-1228402288 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1228402288\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-1953519467 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1953519467\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recentProducts":"<pre class=sf-dump id=sf-dump-1862519279 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1862519279\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","saleProducts":"<pre class=sf-dump id=sf-dump-931615034 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-931615034\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-1620503270 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128241;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/shop?category=electronics</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128085;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/shop?category=fashion</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Living</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#127968;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/shop?category=home</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sports</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#9917;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=sports</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Beauty</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128132;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=beauty</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/shop?category=books</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1620503270\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1419920335 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1419920335\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","defaultCategories":"<pre class=sf-dump id=sf-dump-593090484 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128241;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/shop?category=electronics</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128085;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/shop?category=fashion</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Living</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#127968;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/shop?category=home</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sports</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#9917;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=sports</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Beauty</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128132;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=beauty</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/shop?category=books</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-593090484\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","category":"<pre class=sf-dump id=sf-dump-1588328459 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
  \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/shop?category=books</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1588328459\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php(191): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(343): Illuminate\\Support\\Manager->__call('token', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\layouts\\app.blade.php(6): csrf_token()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\home.blade.php(270): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#45 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php(191): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(343): Illuminate\\Support\\Manager->__call('token', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\a63b44cd76d2d3450c9bfcbf1ec946da.php(6): csrf_token()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\0b6fbff790470416d54f7f2c58b02651.php(272): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#45 {main}
"} 
[2025-06-28 19:22:24] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_1f3...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(24): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:24] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":8,"page":1,"status":"publish","featured":true}} 
[2025-06-28 19:22:26] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_1f3...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(24): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:26] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:22:26] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_69d...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(31): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:26] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","orderby":"popularity","order":"desc"}} 
[2025-06-28 19:22:28] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_69d...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(31): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:28] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:22:31] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('Error fetching ...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(467): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_categories', 3600, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(430): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(39): App\\Services\\WooCommerceService->getCategories()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:31] laravel.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:22:31] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_a72...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(42): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:31] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":6,"page":1,"status":"publish","orderby":"date","order":"desc"}} 
[2025-06-28 19:22:33] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_a72...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(42): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:33] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:22:33] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(722): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->info('Fetching produc...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(137): Illuminate\\Support\\Facades\\Facade::__callStatic('info', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_50c...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(50): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:33] laravel.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","on_sale":true}} 
[2025-06-28 19:22:35] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Log\\LogManager->error('WooCommerce API...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(156): Illuminate\\Support\\Facades\\Facade::__callStatic('error', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(396): App\\Services\\WooCommerceService->App\\Services\\{closure}()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php(429): Illuminate\\Cache\\Repository->remember('wc_products_50c...', 1800, Object(Closure))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Cache\\CacheManager->__call('remember', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Services\\WooCommerceService.php(124): Illuminate\\Support\\Facades\\Facade::__callStatic('remember', Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\app\\Http\\Controllers\\HomeController.php(50): App\\Services\\WooCommerceService->getProducts(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\HomeController->index()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomeController), 'index')
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-06-28 19:22:35] laravel.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:22:35] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(317): Illuminate\\Log\\LogManager->error('Unable to resol...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Spatie\\LaravelIgnition\\Exceptions\\ViewException))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(49): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Spatie\\LaravelIgnition\\Exceptions\\ViewException))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\ViewException))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#28 {main}
"} 
[2025-06-28 19:22:35] laravel.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2","data":{"featuredProducts":"<pre class=sf-dump id=sf-dump-198542131 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-198542131\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","bestSellers":"<pre class=sf-dump id=sf-dump-1826307554 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1826307554\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-685521699 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-685521699\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recentProducts":"<pre class=sf-dump id=sf-dump-654262714 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-654262714\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","saleProducts":"<pre class=sf-dump id=sf-dump-525288963 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-525288963\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-1029785291 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128241;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/shop?category=electronics</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128085;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/shop?category=fashion</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Living</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#127968;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/shop?category=home</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sports</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#9917;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=sports</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Beauty</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128132;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=beauty</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/shop?category=books</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1029785291\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-362422048 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-362422048\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","defaultCategories":"<pre class=sf-dump id=sf-dump-621740923 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128241;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://localhost:8000/shop?category=electronics</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128085;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/shop?category=fashion</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Living</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#127968;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/shop?category=home</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sports</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#9917;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=sports</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Beauty</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128132;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://localhost:8000/shop?category=beauty</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/shop?category=books</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-621740923\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","category":"<pre class=sf-dump id=sf-dump-626235258 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
  \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/shop?category=books</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-626235258\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php(191): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(343): Illuminate\\Support\\Manager->__call('token', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\layouts\\app.blade.php(6): csrf_token()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\home.blade.php(270): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#45 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php(191): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(343): Illuminate\\Support\\Manager->__call('token', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\a63b44cd76d2d3450c9bfcbf1ec946da.php(6): csrf_token()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\0b6fbff790470416d54f7f2c58b02651.php(272): Illuminate\\View\\View->render()
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#45 {main}
"} 
[2025-06-28 19:23:02] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(317): Illuminate\\Log\\LogManager->error('Auth guard [] i...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Spatie\\LaravelIgnition\\Exceptions\\ViewException))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(49): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Spatie\\LaravelIgnition\\Exceptions\\ViewException))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\ViewException))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#28 {main}
"} 
[2025-06-28 19:23:02] laravel.ERROR: Auth guard [] is not defined. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2","data":[]},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Auth guard [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php:86)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\welcome.blade.php(20): Illuminate\\Auth\\AuthManager->guard()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#36 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Auth guard [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php:86)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\41857cbb2521c9c26efb8c110f6caffc.php(20): Illuminate\\Auth\\AuthManager->guard()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#36 {main}
"} 
[2025-06-28 19:23:26] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:212)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(137): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(124): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(681): Illuminate\\Log\\LogManager->driver()
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(317): Illuminate\\Log\\LogManager->error('Unable to resol...', Array)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(278): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(InvalidArgumentException))
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(49): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(InvalidArgumentException))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#28 {main}
"} 
[2025-06-28 19:23:26] laravel.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"exception":"[object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('session.store', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(120): Illuminate\\Container\\Container->offsetGet('session.store')
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('redirect', Array, true)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('redirect', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('redirect', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('redirect')
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(182): Illuminate\\Container\\Container->offsetGet('redirect')
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cont...', Array, true)
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cont...', Array)
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Illuminate\\\\Cont...', Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(120): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cont...', Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(790): app('Illuminate\\\\Cont...')
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\routes\\web.php(23): response()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#49 {main}
"} 
[2025-06-28 19:24:00] local.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"exception":"[object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('session.store', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(120): Illuminate\\Container\\Container->offsetGet('session.store')
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('redirect', Array, true)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('redirect', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('redirect', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('redirect')
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(182): Illuminate\\Container\\Container->offsetGet('redirect')
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cont...', Array, true)
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cont...', Array)
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Illuminate\\\\Cont...', Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(120): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cont...', Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(790): app('Illuminate\\\\Cont...')
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\routes\\web.php(23): response()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#49 {main}
"} 
[2025-06-28 19:27:56] local.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"exception":"[object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('session.store', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(120): Illuminate\\Container\\Container->offsetGet('session.store')
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('redirect', Array, true)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('redirect', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('redirect', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('redirect')
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(182): Illuminate\\Container\\Container->offsetGet('redirect')
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cont...', Array, true)
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cont...', Array)
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Illuminate\\\\Cont...', Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(120): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cont...', Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(790): app('Illuminate\\\\Cont...')
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\routes\\web.php(23): response()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(208): Illuminate\\Routing\\Route->runCallable()
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#49 {main}
"} 
[2025-06-28 19:28:05] local.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"exception":"[object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('session.store', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(120): Illuminate\\Container\\Container->offsetGet('session.store')
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('redirect', Array, true)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('redirect', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('redirect', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('redirect')
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(182): Illuminate\\Container\\Container->offsetGet('redirect')
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cont...', Array, true)
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cont...', Array)
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Illuminate\\\\Cont...', Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(120): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cont...', Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(790): app('Illuminate\\\\Cont...')
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): response()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#40 {main}
"} 
[2025-06-28 19:28:35] local.ERROR: Unable to resolve NULL driver for [Illuminate\Session\SessionManager]. {"exception":"[object] (InvalidArgumentException(code: 0): Unable to resolve NULL driver for [Illuminate\\Session\\SessionManager]. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:71)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('session.store', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(120): Illuminate\\Container\\Container->offsetGet('session.store')
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('redirect', Array, true)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('redirect', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('redirect', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('redirect')
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RoutingServiceProvider.php(182): Illuminate\\Container\\Container->offsetGet('redirect')
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Routing\\RoutingServiceProvider->Illuminate\\Routing\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cont...', Array, true)
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cont...', Array)
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Illuminate\\\\Cont...', Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(120): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cont...', Array)
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(790): app('Illuminate\\\\Cont...')
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): response()
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(146): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#40 {main}
"} 
[2025-06-28 19:30:30] local.ERROR: Command "config:publish" is not defined.

Did you mean one of these?
    config:cache
    config:clear
    config:show
    lang:publish
    sail:publish
    stub:publish
    vendor:publish {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"config:publish\" is not defined.

Did you mean one of these?
    config:cache
    config:clear
    config:show
    lang:publish
    sail:publish
    stub:publish
    vendor:publish at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('config:publish')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-06-28 19:33:40] local.INFO: Fetching products from WooCommerce {"params":{"per_page":3,"page":1,"status":"publish"}} 
[2025-06-28 19:33:43] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:33:45] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:37:09] local.INFO: Fetching products from WooCommerce {"params":{"per_page":3,"page":1,"status":"publish"}} 
[2025-06-28 19:37:12] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:37:13] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:45:26] local.INFO: Fetching products from WooCommerce {"params":{"per_page":24,"page":1,"status":"publish"}} 
[2025-06-28 19:45:31] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:45:33] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:45:34] local.ERROR: Auth guard [] is not defined. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2","data":{"products":"<pre class=sf-dump id=sf-dump-1994733988 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium Wireless Headphones</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"27 characters\">premium-wireless-headphones</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>249.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">High-quality wireless headphones with noise cancellation.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium wireless headphones</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PWH-001</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.5</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>128</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart Fitness Watch</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"19 characters\">smart-fitness-watch</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Advanced fitness tracking with heart rate monitoring.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart fitness watch</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">SFW-002</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.2</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>89</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth Speaker</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"26 characters\">portable-bluetooth-speaker</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>99.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Compact speaker with powerful sound and long battery life.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth speaker</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>75</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PBS-003</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.7</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>156</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1994733988\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-1452164504 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">electronics</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">fashion</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>18</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Garden</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">home-garden</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Sports &amp; Outdoors</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sports-outdoors</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1452164504\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","params":"<pre class=sf-dump id=sf-dump-1305677591 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>per_page</span>\" => <span class=sf-dump-num>24</span>
  \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>
  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">publish</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1305677591\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-2118003782 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium Wireless Headphones</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"27 characters\">premium-wireless-headphones</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>249.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">High-quality wireless headphones with noise cancellation.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium wireless headphones</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PWH-001</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.5</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>128</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart Fitness Watch</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"19 characters\">smart-fitness-watch</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Advanced fitness tracking with heart rate monitoring.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart fitness watch</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">SFW-002</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.2</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>89</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth Speaker</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"26 characters\">portable-bluetooth-speaker</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>99.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Compact speaker with powerful sound and long battery life.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth speaker</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>75</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PBS-003</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.7</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>156</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-2118003782\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","category":"<pre class=sf-dump id=sf-dump-714844888 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Sports &amp; Outdoors</span>\"
  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sports-outdoors</span>\"
  \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>15</span>
  \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-714844888\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1296962267 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1296962267\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","product":"<pre class=sf-dump id=sf-dump-90601648 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
  \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
  \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
  \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
  \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
  </samp>]
  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">electronics</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
  \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
  \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
  \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
  \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
  \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
  \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
  \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
  \"<span class=sf-dump-key>attributes</span>\" => []
  \"<span class=sf-dump-key>variations</span>\" => []
  \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-90601648\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Auth guard [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php:86)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\layouts\\app.blade.php(82): Illuminate\\Auth\\AuthManager->guard()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\shop\\index.blade.php(258): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#44 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Auth guard [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php:86)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\a63b44cd76d2d3450c9bfcbf1ec946da.php(82): Illuminate\\Auth\\AuthManager->guard()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\408c275637e09adf6a5239afc9d5ad33.php(263): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#44 {main}
"} 
[2025-06-28 19:45:45] local.INFO: Fetching products from WooCommerce {"params":{"per_page":8,"page":1,"status":"publish","featured":true}} 
[2025-06-28 19:45:49] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:45:49] local.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","orderby":"popularity","order":"desc"}} 
[2025-06-28 19:45:51] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:45:52] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:45:52] local.INFO: Fetching products from WooCommerce {"params":{"per_page":6,"page":1,"status":"publish","orderby":"date","order":"desc"}} 
[2025-06-28 19:45:54] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:45:54] local.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","on_sale":true}} 
[2025-06-28 19:45:55] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:45:55] local.ERROR: Auth guard [] is not defined. {"view":{"view":"C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2","data":{"featuredProducts":"<pre class=sf-dump id=sf-dump-1316333307 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium Wireless Headphones</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"27 characters\">premium-wireless-headphones</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>249.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">High-quality wireless headphones with noise cancellation.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium wireless headphones</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PWH-001</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.5</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>128</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth Speaker</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"26 characters\">portable-bluetooth-speaker</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>99.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Compact speaker with powerful sound and long battery life.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth speaker</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>75</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PBS-003</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.7</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>156</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1316333307\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","bestSellers":"<pre class=sf-dump id=sf-dump-699281821 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium Wireless Headphones</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"27 characters\">premium-wireless-headphones</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>249.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">High-quality wireless headphones with noise cancellation.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium wireless headphones</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PWH-001</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.5</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>128</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart Fitness Watch</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"19 characters\">smart-fitness-watch</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Advanced fitness tracking with heart rate monitoring.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart fitness watch</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">SFW-002</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.2</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>89</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth Speaker</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"26 characters\">portable-bluetooth-speaker</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>99.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Compact speaker with powerful sound and long battery life.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth speaker</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>75</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PBS-003</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.7</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>156</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-699281821\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-1410206875 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">electronics</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">fashion</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>18</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Garden</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">home-garden</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>12</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Sports &amp; Outdoors</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"15 characters\">sports-outdoors</span>\"
    \"<span class=sf-dump-key>count</span>\" => <span class=sf-dump-num>15</span>
    \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-1410206875\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","recentProducts":"<pre class=sf-dump id=sf-dump-170762358 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium Wireless Headphones</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"27 characters\">premium-wireless-headphones</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>249.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">High-quality wireless headphones with noise cancellation.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium wireless headphones</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PWH-001</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.5</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>128</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart Fitness Watch</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"19 characters\">smart-fitness-watch</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Advanced fitness tracking with heart rate monitoring.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart fitness watch</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">SFW-002</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.2</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>89</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth Speaker</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"26 characters\">portable-bluetooth-speaker</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>99.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Compact speaker with powerful sound and long battery life.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth speaker</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>75</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PBS-003</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.7</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>156</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-170762358\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","saleProducts":"<pre class=sf-dump id=sf-dump-284509576 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium Wireless Headphones</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"27 characters\">premium-wireless-headphones</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>249.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>199.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"57 characters\">High-quality wireless headphones with noise cancellation.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Premium wireless headphones</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>50</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PWH-001</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.5</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>128</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart Fitness Watch</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"19 characters\">smart-fitness-watch</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>299.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Advanced fitness tracking with heart rate monitoring.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Smart fitness watch</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>25</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">SFW-002</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.2</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>89</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>3</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth Speaker</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"26 characters\">portable-bluetooth-speaker</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>99.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-num>79.99</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"58 characters\">Compact speaker with powerful sound and long battery life.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Portable Bluetooth speaker</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>75</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">PBS-003</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.7</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>156</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
    \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
    \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
    </samp>]
    \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [ &#8230;3]
    </samp>]
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
    \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
    \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
    \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
    \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
    \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
    \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
    \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
    \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
    \"<span class=sf-dump-key>attributes</span>\" => []
    \"<span class=sf-dump-key>variations</span>\" => []
    \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-284509576\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","__currentLoopData":"<pre class=sf-dump id=sf-dump-106815673 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128241;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/shop?category=electronics</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128085;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/shop?category=fashion</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Living</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#127968;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/shop?category=home</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sports</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#9917;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/shop?category=sports</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Beauty</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128132;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/shop?category=beauty</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/shop?category=books</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-106815673\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","product":"<pre class=sf-dump id=sf-dump-1080171079 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>
  \"<span class=sf-dump-key>woocommerce_id</span>\" => <span class=sf-dump-num>4</span>
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless Charging Pad</span>\"
  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"21 characters\">wireless-charging-pad</span>\"
  \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-num>39.99</span>
  \"<span class=sf-dump-key>regular_price</span>\" => <span class=sf-dump-num>39.99</span>
  \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&amp;h=600&amp;fit=crop</span>\"
  </samp>]
  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
      \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"11 characters\">electronics</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Fast wireless charging for compatible devices.</span>\"
  \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Wireless charging pad</span>\"
  \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">instock</span>\"
  \"<span class=sf-dump-key>stock_quantity</span>\" => <span class=sf-dump-num>100</span>
  \"<span class=sf-dump-key>in_stock</span>\" => <span class=sf-dump-const>true</span>
  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"7 characters\">WCP-004</span>\"
  \"<span class=sf-dump-key>featured</span>\" => <span class=sf-dump-const>false</span>
  \"<span class=sf-dump-key>rating</span>\" => <span class=sf-dump-num>4.0</span>
  \"<span class=sf-dump-key>review_count</span>\" => <span class=sf-dump-num>67</span>
  \"<span class=sf-dump-key>attributes</span>\" => []
  \"<span class=sf-dump-key>variations</span>\" => []
  \"<span class=sf-dump-key>has_variations</span>\" => <span class=sf-dump-const>false</span>
</samp>]
</pre><script>Sfdump(\"sf-dump-1080171079\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","loop":"<pre class=sf-dump id=sf-dump-1963591673 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1963591673\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","index":"<pre class=sf-dump id=sf-dump-750183316 data-indent-pad=\"  \"><span class=sf-dump-num>3</span>
</pre><script>Sfdump(\"sf-dump-750183316\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","defaultCategories":"<pre class=sf-dump id=sf-dump-646802395 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Electronics</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128241;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/shop?category=electronics</span>\"
  </samp>]
  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Fashion</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128085;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/shop?category=fashion</span>\"
  </samp>]
  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Home &amp; Living</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#127968;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/shop?category=home</span>\"
  </samp>]
  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Sports</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#9917;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/shop?category=sports</span>\"
  </samp>]
  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Beauty</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128132;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/shop?category=beauty</span>\"
  </samp>]
  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
    \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
    \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/shop?category=books</span>\"
  </samp>]
</samp>]
</pre><script>Sfdump(\"sf-dump-646802395\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","category":"<pre class=sf-dump id=sf-dump-640889991 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Books</span>\"
  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str>&#128218;</span>\"
  \"<span class=sf-dump-key>href</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/shop?category=books</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-640889991\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Auth guard [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php:86)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\layouts\\app.blade.php(82): Illuminate\\Auth\\AuthManager->guard()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\resources\\views\\home.blade.php(270): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#44 {main}

[previous exception] [object] (InvalidArgumentException(code: 0): Auth guard [] is not defined. at C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php:86)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(70): Illuminate\\Auth\\AuthManager->resolve(NULL)
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\a63b44cd76d2d3450c9bfcbf1ec946da.php(82): Illuminate\\Auth\\AuthManager->guard()
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\storage\\framework\\views\\0b6fbff790470416d54f7f2c58b02651.php(272): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (2)\\laravel-project\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#44 {main}
"} 
[2025-06-28 19:46:57] local.INFO: Fetching products from WooCommerce {"params":{"per_page":24,"page":1,"status":"publish"}} 
[2025-06-28 19:46:59] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:00] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:47:03] local.INFO: Fetching products from WooCommerce {"params":{"per_page":8,"page":1,"status":"publish","featured":true}} 
[2025-06-28 19:47:05] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:05] local.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","orderby":"popularity","order":"desc"}} 
[2025-06-28 19:47:07] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:10] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:47:10] local.INFO: Fetching products from WooCommerce {"params":{"per_page":6,"page":1,"status":"publish","orderby":"date","order":"desc"}} 
[2025-06-28 19:47:12] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:12] local.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","on_sale":true}} 
[2025-06-28 19:47:13] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:15] local.INFO: Fetching products from WooCommerce {"params":{"per_page":8,"page":1,"status":"publish","featured":true}} 
[2025-06-28 19:47:17] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:17] local.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","orderby":"popularity","order":"desc"}} 
[2025-06-28 19:47:18] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:21] local.ERROR: Error fetching categories from WooCommerce: 401  
[2025-06-28 19:47:21] local.INFO: Fetching products from WooCommerce {"params":{"per_page":6,"page":1,"status":"publish","orderby":"date","order":"desc"}} 
[2025-06-28 19:47:24] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:24] local.INFO: Fetching products from WooCommerce {"params":{"per_page":4,"page":1,"status":"publish","on_sale":true}} 
[2025-06-28 19:47:26] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:53] local.INFO: Fetching products from WooCommerce {"params":{"per_page":24,"page":1,"status":"publish"}} 
[2025-06-28 19:47:55] local.ERROR: WooCommerce API Error - Products: {"status":401,"body":"{\"code\":\"woocommerce_rest_cannot_view\",\"message\":\"Sorry, you cannot list resources.\",\"data\":{\"status\":401}}"} 
[2025-06-28 19:47:58] local.ERROR: Error fetching categories from WooCommerce: 401  
