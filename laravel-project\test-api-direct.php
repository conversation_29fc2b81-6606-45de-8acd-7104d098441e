<?php

// Direct test of WooCommerce API with your exact credentials
$storeUrl = 'https://deal4u.co';
$consumerKey = 'ck_b807a369ee952d78eb8779fbc2b3dd11e6b9d280';
$consumerSecret = 'cs_850b5221ea75ceffe929c7a81eb6d7e6e304f343';

echo "Testing WooCommerce API with your credentials...\n\n";

// Test 1: Products endpoint
echo "1. Testing Products Endpoint:\n";
$url = $storeUrl . '/wp-json/wc/v3/products?per_page=1';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_USERPWD, $consumerKey . ':' . $consumerSecret);
curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "URL: $url\n";
echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: " . substr($response, 0, 500) . "...\n\n";

// Test 2: System Status endpoint (usually requires less permissions)
echo "2. Testing System Status Endpoint:\n";
$url2 = $storeUrl . '/wp-json/wc/v3/system_status';

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $url2);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_USERPWD, $consumerKey . ':' . $consumerSecret);
curl_setopt($ch2, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch2, CURLOPT_TIMEOUT, 30);
curl_setopt($ch2, CURLOPT_SSL_VERIFYPEER, false);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
$error2 = curl_error($ch2);
curl_close($ch2);

echo "URL: $url2\n";
echo "HTTP Code: $httpCode2\n";
if ($error2) {
    echo "cURL Error: $error2\n";
}
echo "Response: " . substr($response2, 0, 500) . "...\n\n";

// Test 3: Data endpoint (public data)
echo "3. Testing Data Endpoint:\n";
$url3 = $storeUrl . '/wp-json/wc/v3/data';

$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, $url3);
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_USERPWD, $consumerKey . ':' . $consumerSecret);
curl_setopt($ch3, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch3, CURLOPT_TIMEOUT, 30);
curl_setopt($ch3, CURLOPT_SSL_VERIFYPEER, false);

$response3 = curl_exec($ch3);
$httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
$error3 = curl_error($ch3);
curl_close($ch3);

echo "URL: $url3\n";
echo "HTTP Code: $httpCode3\n";
if ($error3) {
    echo "cURL Error: $error3\n";
}
echo "Response: " . substr($response3, 0, 500) . "...\n\n";

echo "Test completed.\n";
