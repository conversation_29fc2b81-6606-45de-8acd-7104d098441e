<?php $__env->startSection('title', 'My Wishlist'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Wishlist</h1>
            <p class="text-gray-600 mt-2">Save your favorite products for later</p>
        </div>

        <?php if(empty($wishlistItems)): ?>
            <div class="bg-white rounded-lg shadow-md p-8 text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-6">
                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                
                <h2 class="text-xl font-semibold text-gray-900 mb-2">Your wishlist is empty</h2>
                <p class="text-gray-600 mb-6">Start adding products you love to your wishlist</p>
                
                <a href="<?php echo e(route('shop.index')); ?>" 
                   class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Browse Products
                </a>
            </div>
        <?php else: ?>
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">
                            <?php echo e(count($wishlistItems)); ?> <?php echo e(count($wishlistItems) === 1 ? 'item' : 'items'); ?>

                        </h2>
                        
                        <button onclick="clearWishlist()" 
                                class="text-red-600 hover:text-red-800 text-sm font-medium">
                            Clear All
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <?php $__currentLoopData = $wishlistItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200">
                            <div class="relative">
                                <?php if(!empty($product['images']) && isset($product['images'][0]['src'])): ?>
                                    <img src="<?php echo e($product['images'][0]['src']); ?>" 
                                         alt="<?php echo e($product['name']); ?>" 
                                         class="w-full h-48 object-cover">
                                <?php else: ?>
                                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                        <span class="text-gray-400">No Image</span>
                                    </div>
                                <?php endif; ?>
                                
                                <button onclick="removeFromWishlist(<?php echo e($product['id']); ?>)" 
                                        class="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
                                    <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M6 18L18 6M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="p-4">
                                <h3 class="text-sm font-medium text-gray-900 mb-2 line-clamp-2">
                                    <?php echo e($product['name']); ?>

                                </h3>
                                
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-lg font-bold text-gray-900">
                                        €<?php echo e(number_format($product['price'], 2)); ?>

                                    </span>
                                    
                                    <?php if(isset($product['sale_price']) && $product['sale_price'] < $product['regular_price']): ?>
                                        <span class="text-sm text-gray-500 line-through">
                                            €<?php echo e(number_format($product['regular_price'], 2)); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="flex space-x-2">
                                    <button onclick="addToCart(<?php echo e($product['id']); ?>, '<?php echo e(addslashes($product['name'])); ?>', <?php echo e($product['price']); ?>)" 
                                            class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        Add to Cart
                                    </button>
                                    
                                    <a href="<?php echo e(route('product.show', $product['id'])); ?>" 
                                       class="px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function removeFromWishlist(productId) {
    fetch('/wishlist/remove', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function clearWishlist() {
    if (confirm('Are you sure you want to clear your entire wishlist?')) {
        fetch('/wishlist/clear', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
}

function addToCart(productId, productName, price) {
    fetch('/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            name: productName,
            price: price,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Product added to cart!');
            // Update cart count if you have a cart counter
            updateCartCount();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function updateCartCount() {
    fetch('/cart/count')
        .then(response => response.json())
        .then(data => {
            const cartCountElement = document.querySelector('#cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
            }
        });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/wishlist/index.blade.php ENDPATH**/ ?>