<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class PayPalController extends Controller
{
    public function checkout(Request $request)
    {
        $pendingOrder = Session::get('pending_order');
        
        if (!$pendingOrder) {
            return redirect()->route('cart.index')->with('error', 'No pending order found.');
        }

        return view('payments.paypal.checkout', [
            'order' => $pendingOrder,
            'total' => $pendingOrder['total'],
            'clientId' => env('PAYPAL_CLIENT_ID', 'demo_client_id')
        ]);
    }

    public function process(Request $request)
    {
        $request->validate([
            'paypal_order_id' => 'required',
            'payer_id' => 'required'
        ]);

        $pendingOrder = Session::get('pending_order');
        
        if (!$pendingOrder) {
            return redirect()->route('cart.index')->with('error', 'No pending order found.');
        }

        // Here you would integrate with PayPal API
        // For demo purposes, we'll simulate a successful payment
        
        try {
            // Simulate PayPal payment capture
            $payment = [
                'id' => $request->paypal_order_id,
                'payer_id' => $request->payer_id,
                'amount' => $pendingOrder['total'],
                'currency' => 'EUR',
                'status' => 'COMPLETED'
            ];

            // Create order in WooCommerce or local database
            $orderId = $this->createOrder($pendingOrder, $payment);

            // Clear cart and pending order
            Session::forget(['cart', 'pending_order']);

            return redirect()->route('paypal.success', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }

    public function success(Request $request)
    {
        $orderId = $request->get('order_id', 'N/A');
        return view('payments.paypal.success', compact('orderId'));
    }

    public function cancel()
    {
        return view('payments.paypal.cancel');
    }

    private function createOrder($orderData, $payment)
    {
        // Add payment information to order data
        $orderData['payment_details'] = [
            'method' => 'paypal',
            'transaction_id' => $payment['id'],
            'payer_id' => $payment['payer_id'],
            'amount' => $payment['amount'],
            'currency' => $payment['currency'],
            'status' => $payment['status']
        ];

        // Try to create order in WooCommerce
        try {
            $wooCommerceService = app(\App\Services\WooCommerceService::class);
            $order = $wooCommerceService->createOrder($orderData);
            return $order['id'] ?? uniqid('paypal_order_');
        } catch (\Exception $e) {
            // Fallback to local order creation
            return uniqid('paypal_order_');
        }
    }
}
