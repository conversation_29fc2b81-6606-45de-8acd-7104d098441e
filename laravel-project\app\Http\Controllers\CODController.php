<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CODController extends Controller
{
    public function success(Request $request)
    {
        $orderId = $request->get('order_id', 'N/A');
        return view('payments.cod.success', compact('orderId'));
    }

    public function instructions(Request $request)
    {
        $orderId = $request->get('order_id', 'N/A');
        $total = $request->get('total', 0);
        
        return view('payments.cod.instructions', compact('orderId', 'total'));
    }
}
