@extends('layouts.app')

@section('title', 'Order Confirmed - Cash on Delivery')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-md p-8 text-center">
            <!-- Success Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Order Confirmed!</h1>
            
            <p class="text-lg text-gray-600 mb-6">
                Your order has been confirmed. You will pay cash when your order is delivered.
            </p>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                <h2 class="text-lg font-semibold text-yellow-900 mb-2">Cash on Delivery Instructions</h2>
                <div class="text-left text-yellow-800 space-y-2">
                    <p>• Please have the exact amount ready when the delivery arrives</p>
                    <p>• A delivery fee of €5 has been added to your order total</p>
                    <p>• Our delivery partner will contact you before delivery</p>
                    <p>• Payment is due upon receipt of your order</p>
                </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-2">Order Details</h2>
                <p class="text-gray-600">
                    <strong>Order ID:</strong> {{ $orderId }}
                </p>
                <p class="text-gray-600">
                    <strong>Payment Method:</strong> Cash on Delivery
                </p>
                <p class="text-gray-600">
                    <strong>Date:</strong> {{ now()->format('F j, Y') }}
                </p>
            </div>
            
            <div class="space-y-4">
                <p class="text-sm text-gray-500">
                    You will receive an email confirmation shortly with your order details and tracking information.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('home') }}" 
                       class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Continue Shopping
                    </a>
                    
                    @auth
                    <a href="{{ route('dashboard') }}" 
                       class="bg-gray-600 text-white px-6 py-3 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        View Orders
                    </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
