<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class StripeController extends Controller
{
    public function checkout(Request $request)
    {
        $pendingOrder = Session::get('pending_order');
        
        if (!$pendingOrder) {
            return redirect()->route('cart.index')->with('error', 'No pending order found.');
        }

        return view('payments.stripe.checkout', [
            'order' => $pendingOrder,
            'total' => $pendingOrder['total'],
            'publishableKey' => env('STRIPE_PUBLISHABLE_KEY', 'pk_test_demo')
        ]);
    }

    public function process(Request $request)
    {
        $request->validate([
            'stripeToken' => 'required',
            'stripeEmail' => 'required|email'
        ]);

        $pendingOrder = Session::get('pending_order');
        
        if (!$pendingOrder) {
            return redirect()->route('cart.index')->with('error', 'No pending order found.');
        }

        // Here you would integrate with Stripe API
        // For demo purposes, we'll simulate a successful payment
        
        try {
            // Simulate Stripe charge
            $charge = [
                'id' => 'ch_' . uniqid(),
                'amount' => $pendingOrder['total'] * 100, // Stripe uses cents
                'currency' => 'eur',
                'status' => 'succeeded'
            ];

            // Create order in WooCommerce or local database
            $orderId = $this->createOrder($pendingOrder, $charge);

            // Clear cart and pending order
            Session::forget(['cart', 'pending_order']);

            return redirect()->route('stripe.success', ['order_id' => $orderId]);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Payment failed: ' . $e->getMessage());
        }
    }

    public function success(Request $request)
    {
        $orderId = $request->get('order_id', 'N/A');
        return view('payments.stripe.success', compact('orderId'));
    }

    public function cancel()
    {
        return view('payments.stripe.cancel');
    }

    private function createOrder($orderData, $charge)
    {
        // Add payment information to order data
        $orderData['payment_details'] = [
            'method' => 'stripe',
            'transaction_id' => $charge['id'],
            'amount' => $charge['amount'] / 100,
            'currency' => $charge['currency'],
            'status' => $charge['status']
        ];

        // Try to create order in WooCommerce
        try {
            $wooCommerceService = app(\App\Services\WooCommerceService::class);
            $order = $wooCommerceService->createOrder($orderData);
            return $order['id'] ?? uniqid('stripe_order_');
        } catch (\Exception $e) {
            // Fallback to local order creation
            return uniqid('stripe_order_');
        }
    }
}
