@extends('layouts.app')

@section('title', 'Deal4u - Amazing Deals on Premium Products')
@section('description', 'Discover thousands of high-quality products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.')

@section('content')
<!-- Summer Sale Banner -->
<div class="bg-yellow-400 text-purple-900 py-4 relative overflow-hidden">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
            <div class="flex items-center gap-3">
                <div class="bg-purple-900 text-yellow-400 text-xl font-extrabold p-2 rounded-lg rotate-3 shadow-lg">
                    50% OFF
                </div>
                <div>
                    <h3 class="text-lg md:text-xl font-bold">SUMMER MEGA SALE</h3>
                    <p class="text-sm">Limited time offer on all products!</p>
                </div>
            </div>
            <a href="{{ route('shop.index', ['sale' => 'true']) }}" 
               class="bg-purple-900 hover:bg-purple-800 text-white font-bold py-2 px-6 rounded-lg shadow-md transition-colors">
                Shop Now
            </a>
        </div>
    </div>
    <!-- Decorative elements -->
    <div class="absolute -right-8 top-0 bottom-0 w-32 bg-purple-900 opacity-20 rotate-12"></div>
    <div class="absolute left-1/3 top-0 bottom-0 w-8 bg-purple-900 opacity-10 -rotate-12"></div>
</div>

<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-600 via-purple-700 to-indigo-800 text-white py-20 overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-20 h-20 bg-yellow-400 rounded-full animate-pulse"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-pink-400 rounded-full animate-bounce"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-green-400 rounded-full animate-ping"></div>
        <div class="absolute bottom-32 right-1/3 w-8 h-8 bg-blue-300 rounded-full animate-pulse"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="text-center">
            <div class="animate-fade-in-up">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    Amazing Deals on<br>
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-400 animate-pulse">
                        Premium Products
                    </span>
                </h1>
            </div>

            <div class="animate-fade-in-up animation-delay-200">
                <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-gray-100">
                    Discover thousands of high-quality gaming consoles, electronics, and tech products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.
                </p>
            </div>

            <div class="animate-fade-in-up animation-delay-400">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('shop.index') }}"
                       class="group bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-300 hover:to-orange-400 text-purple-900 font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <span class="flex items-center justify-center">
                            Shop Now
                            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </span>
                    </a>
                    <a href="{{ route('categories.index') }}"
                       class="group border-2 border-white hover:bg-white hover:text-purple-700 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <span class="flex items-center justify-center">
                            Browse Categories
                            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Deal4u?</h2>
            <p class="text-lg text-gray-600">We're committed to providing you with the best shopping experience</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Free Shipping</h3>
                <p class="text-gray-600">On orders over $50</p>
            </div>
            
            <div class="text-center">
                <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Quality Guarantee</h3>
                <p class="text-gray-600">Authentic products only</p>
            </div>
            
            <div class="text-center">
                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Easy Returns</h3>
                <p class="text-gray-600">30-day return policy</p>
            </div>
            
            <div class="text-center">
                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Secure Payment</h3>
                <p class="text-gray-600">SSL protected checkout</p>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
                <div class="text-4xl font-bold text-blue-600 mb-2">10,000+</div>
                <div class="text-lg text-gray-600">Happy Customers</div>
            </div>
            <div>
                <div class="text-4xl font-bold text-blue-600 mb-2">50,000+</div>
                <div class="text-lg text-gray-600">Products Sold</div>
            </div>
            <div>
                <div class="text-4xl font-bold text-blue-600 mb-2">99.9%</div>
                <div class="text-lg text-gray-600">Customer Satisfaction</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover our hand-picked selection of premium products
            </p>
        </div>
        
        @if(count($featuredProducts) > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($featuredProducts as $product)
                    @include('components.product-card', ['product' => $product])
                @endforeach
            </div>
            
            <div class="text-center mt-12">
                <a href="{{ route('shop.index') }}" 
                   class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                    View All Products
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No featured products available</h3>
                <p class="mt-2 text-gray-600">Please check back later for new products.</p>
                <div class="mt-6">
                    <a href="{{ route('shop.index') }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Browse All Products
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Best Sellers Section -->
@if(count($bestSellers) > 0)
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Best Sellers</h2>
            <p class="text-lg text-gray-600">Our most popular products loved by customers</p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($bestSellers as $index => $product)
                <div class="relative">
                    <div class="absolute top-2 left-2 z-10">
                        <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                            #{{ $index + 1 }} Best Seller
                        </span>
                    </div>
                    @include('components.product-card', ['product' => $product])
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Categories Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
            <p class="text-lg text-gray-600">Find exactly what you're looking for</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            @php
                $categoryIcons = [
                    'electronics' => '📱',
                    'handheld-game-players' => '🎮',
                    'smart-watches' => '⌚',
                    'smart-watches-accessories' => '📱',
                    'video-game-consoles' => '🎮',
                ];
            @endphp

            @if(count($categories) > 0)
                @foreach($categories as $category)
                    @if($category['count'] > 0)
                        <a href="{{ route('shop.index', ['category' => $category['id']]) }}"
                           class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 text-center hover:from-blue-50 hover:to-blue-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105 border border-gray-200">
                            <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">
                                {{ $categoryIcons[$category['slug']] ?? '📦' }}
                            </div>
                            <h3 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600 mb-1">
                                {{ $category['name'] }}
                            </h3>
                            <p class="text-xs text-gray-500">
                                {{ $category['count'] }} products
                            </p>
                        </a>
                    @endif
                @endforeach
            @else
                @php
                    $defaultCategories = [
                        ['name' => 'Electronics', 'icon' => '📱', 'href' => route('shop.index', ['category' => 'electronics'])],
                        ['name' => 'Gaming', 'icon' => '🎮', 'href' => route('shop.index', ['category' => 'gaming'])],
                        ['name' => 'Smart Watches', 'icon' => '⌚', 'href' => route('shop.index', ['category' => 'watches'])],
                        ['name' => 'Accessories', 'icon' => '🔌', 'href' => route('shop.index', ['category' => 'accessories'])],
                        ['name' => 'Tech Gadgets', 'icon' => '💻', 'href' => route('shop.index', ['category' => 'tech'])],
                    ];
                @endphp

                @foreach($defaultCategories as $category)
                    <a href="{{ $category['href'] }}"
                       class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 text-center hover:from-blue-50 hover:to-blue-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105 border border-gray-200">
                        <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">
                            {{ $category['icon'] }}
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600">
                            {{ $category['name'] }}
                        </h3>
                    </a>
                @endforeach
            @endif
        </div>
    </div>
</section>

<!-- Promotional Banner -->
<section class="py-16 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 overflow-hidden relative">
    <div class="absolute inset-0 opacity-20" style="background-image: url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80'); background-size: cover; background-position: center;"></div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="md:flex items-center justify-between">
            <div class="md:w-1/2 text-center md:text-left mb-8 md:mb-0">
                <span class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm text-white text-xs font-semibold rounded-full mb-3">LIMITED TIME OFFER</span>
                <h2 class="text-4xl md:text-5xl font-extrabold text-white mb-4 leading-tight">
                    Summer Sale is<br><span class="text-yellow-300">Now Live!</span>
                </h2>
                <p class="text-xl text-white text-opacity-90 mb-8 max-w-md">
                    Enjoy up to 50% off on selected items across all categories. Don't miss out on these incredible deals!
                </p>
                <a href="{{ route('shop.index', ['sale' => 'true']) }}" 
                   class="inline-flex items-center px-8 py-4 rounded-lg bg-white text-purple-600 font-bold text-lg hover:bg-yellow-300 hover:text-purple-700 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    Shop Now
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
            <div class="md:w-1/2 flex justify-center">
                <div class="w-64 h-64 sm:w-80 sm:h-80 relative rounded-full bg-white bg-opacity-20 backdrop-blur-sm p-2 transform rotate-3 hover:rotate-6 transition-transform">
                    <div class="w-full h-full rounded-full overflow-hidden border-4 border-white border-opacity-40">
                        <div class="w-full h-full bg-gradient-to-br from-yellow-400 to-pink-500 flex items-center justify-center text-white text-opacity-90 text-center p-6">
                            <div>
                                <p class="text-4xl font-extrabold">50%</p>
                                <p class="text-xl font-bold">OFF</p>
                                <p class="text-sm mt-2 font-medium">Selected Items</p>
                                <p class="text-xs mt-3 font-bold">Use code: SUMMER50</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
