@extends('layouts.app')

@section('title', 'Deal4u - Amazing Deals on Premium Products')
@section('description', 'Discover thousands of high-quality products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.')

@section('content')
<!-- Hero Section with Sale Banner -->
<section class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
    <!-- NEW SALE RIBBON - Prominent at the top -->
    <div class="absolute top-0 left-0 right-0 bg-yellow-400 text-purple-900 py-2 px-4 text-center font-bold transform -skew-y-2 shadow-lg z-30">
        <div class="container mx-auto flex items-center justify-center gap-4">
            <span class="animate-pulse">🔥</span>
            <span class="text-lg uppercase tracking-wider">SUMMER SALE: UP TO 50% OFF EVERYTHING</span>
            <span class="animate-pulse">🔥</span>
        </div>
    </div>

    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10">
        <!-- Promotional Banner - Floating -->
        <div class="absolute top-4 right-4 md:top-8 md:right-8 animate-bounce-slow">
            <div class="relative w-24 h-24 md:w-32 md:h-32 bg-yellow-400 rounded-full flex items-center justify-center transform rotate-12 shadow-xl">
                <div class="text-center px-2">
                    <p class="text-xl md:text-2xl font-black text-purple-900 leading-none">50%</p>
                    <p class="text-sm md:text-base font-bold text-purple-900 leading-none">OFF</p>
                    <p class="text-[10px] md:text-xs mt-1 font-semibold text-purple-800">SUMMER50</p>
                </div>
            </div>
        </div>

        <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-2/3 text-center md:text-left">
                <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-6 animate-pulse">
                    <span class="text-xs font-semibold tracking-wider">🔥 SUMMER SALE NOW LIVE 🔥</span>
                </div>

                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Premium Products,
                    <span class="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                        Amazing Deals
                    </span>
                </h1>

                <p class="text-lg md:text-xl mb-8 text-blue-100 max-w-2xl">
                    Discover thousands of high-quality gaming consoles, electronics, and tech products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.
                </p>

                <div class="flex flex-col sm:flex-row gap-4 mb-8">
                    <a href="{{ route('shop.index') }}"
                       class="group bg-yellow-400 hover:bg-yellow-300 text-purple-900 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center">
                        Shop Now
                        <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                    <a href="{{ route('categories.index') }}"
                       class="group border-2 border-white hover:bg-white hover:text-purple-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center">
                        Browse Categories
                        <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </a>
                </div>

                <!-- Trust indicators -->
                <div class="flex flex-wrap items-center justify-center md:justify-start gap-6 text-sm text-blue-200">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        <span>4.9/5 Rating</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>10,000+ Happy Customers</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <span>Free Shipping</span>
                    </div>
                </div>
            </div>

            <!-- Hero Image/Visual -->
            <div class="md:w-1/3 mt-8 md:mt-0 relative">
                <div class="relative w-full max-w-md mx-auto">
                    <!-- Floating product cards -->
                    <div class="absolute -top-4 -left-4 w-24 h-32 bg-white rounded-lg shadow-xl transform rotate-12 animate-float">
                        <div class="p-2">
                            <div class="w-full h-16 bg-gray-200 rounded mb-2"></div>
                            <div class="h-2 bg-gray-200 rounded mb-1"></div>
                            <div class="h-2 bg-gray-200 rounded w-2/3"></div>
                        </div>
                    </div>
                    <div class="absolute -top-2 -right-6 w-28 h-36 bg-white rounded-lg shadow-xl transform -rotate-6 animate-float-delayed">
                        <div class="p-2">
                            <div class="w-full h-20 bg-gray-200 rounded mb-2"></div>
                            <div class="h-2 bg-gray-200 rounded mb-1"></div>
                            <div class="h-2 bg-gray-200 rounded w-3/4"></div>
                        </div>
                    </div>

                    <!-- Main visual -->
                    <div class="relative z-10 bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 text-center">
                        <div class="text-6xl mb-4">🎮</div>
                        <h3 class="text-xl font-bold mb-2">Gaming Consoles</h3>
                        <p class="text-blue-200 text-sm">Latest models available</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose Deal4u?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">We're committed to providing you with the best shopping experience with premium products and exceptional service</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center group">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Free Shipping</h3>
                <p class="text-gray-600 leading-relaxed">Fast and free shipping on all orders over $50. Get your products delivered right to your door.</p>
            </div>

            <div class="text-center group">
                <div class="bg-gradient-to-br from-green-500 to-green-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Quality Guarantee</h3>
                <p class="text-gray-600 leading-relaxed">100% authentic products with quality guarantee. We stand behind every product we sell.</p>
            </div>

            <div class="text-center group">
                <div class="bg-gradient-to-br from-purple-500 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Easy Returns</h3>
                <p class="text-gray-600 leading-relaxed">Hassle-free 30-day return policy. Not satisfied? Return it for a full refund.</p>
            </div>

            <div class="text-center group">
                <div class="bg-gradient-to-br from-orange-500 to-orange-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 transform group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-3">Secure Payment</h3>
                <p class="text-gray-600 leading-relaxed">Your payment information is protected with bank-level SSL encryption and security.</p>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
                <div class="text-4xl font-bold text-blue-600 mb-2">10,000+</div>
                <div class="text-lg text-gray-600">Happy Customers</div>
            </div>
            <div>
                <div class="text-4xl font-bold text-blue-600 mb-2">50,000+</div>
                <div class="text-lg text-gray-600">Products Sold</div>
            </div>
            <div>
                <div class="text-4xl font-bold text-blue-600 mb-2">99.9%</div>
                <div class="text-lg text-gray-600">Customer Satisfaction</div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Products</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover our hand-picked selection of premium products
            </p>
        </div>
        
        @if(count($featuredProducts) > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($featuredProducts as $product)
                    @include('components.product-card', ['product' => $product])
                @endforeach
            </div>
            
            <div class="text-center mt-12">
                <a href="{{ route('shop.index') }}" 
                   class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                    View All Products
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No featured products available</h3>
                <p class="mt-2 text-gray-600">Please check back later for new products.</p>
                <div class="mt-6">
                    <a href="{{ route('shop.index') }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Browse All Products
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Best Sellers Section -->
@if(count($bestSellers) > 0)
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Best Sellers</h2>
            <p class="text-lg text-gray-600">Our most popular products loved by customers</p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($bestSellers as $index => $product)
                <div class="relative">
                    <div class="absolute top-2 left-2 z-10">
                        <span class="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                            #{{ $index + 1 }} Best Seller
                        </span>
                    </div>
                    @include('components.product-card', ['product' => $product])
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Categories Section -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Shop by Category</h2>
            <p class="text-lg text-gray-600">Find exactly what you're looking for</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            @php
                $categoryIcons = [
                    'electronics' => '📱',
                    'handheld-game-players' => '🎮',
                    'smart-watches' => '⌚',
                    'smart-watches-accessories' => '📱',
                    'video-game-consoles' => '🎮',
                ];
            @endphp

            @if(count($categories) > 0)
                @foreach($categories as $category)
                    @if($category['count'] > 0)
                        <a href="{{ route('shop.index', ['category' => $category['id']]) }}"
                           class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 text-center hover:from-blue-50 hover:to-blue-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105 border border-gray-200">
                            <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">
                                {{ $categoryIcons[$category['slug']] ?? '📦' }}
                            </div>
                            <h3 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600 mb-1">
                                {{ $category['name'] }}
                            </h3>
                            <p class="text-xs text-gray-500">
                                {{ $category['count'] }} products
                            </p>
                        </a>
                    @endif
                @endforeach
            @else
                @php
                    $defaultCategories = [
                        ['name' => 'Electronics', 'icon' => '📱', 'href' => route('shop.index', ['category' => 'electronics'])],
                        ['name' => 'Gaming', 'icon' => '🎮', 'href' => route('shop.index', ['category' => 'gaming'])],
                        ['name' => 'Smart Watches', 'icon' => '⌚', 'href' => route('shop.index', ['category' => 'watches'])],
                        ['name' => 'Accessories', 'icon' => '🔌', 'href' => route('shop.index', ['category' => 'accessories'])],
                        ['name' => 'Tech Gadgets', 'icon' => '💻', 'href' => route('shop.index', ['category' => 'tech'])],
                    ];
                @endphp

                @foreach($defaultCategories as $category)
                    <a href="{{ $category['href'] }}"
                       class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 text-center hover:from-blue-50 hover:to-blue-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105 border border-gray-200">
                        <div class="text-4xl mb-3 group-hover:scale-110 transition-transform">
                            {{ $category['icon'] }}
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 group-hover:text-blue-600">
                            {{ $category['name'] }}
                        </h3>
                    </a>
                @endforeach
            @endif
        </div>
    </div>
</section>

<!-- Promotional Banner -->
<section class="py-16 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 overflow-hidden relative">
    <div class="absolute inset-0 opacity-20" style="background-image: url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1740&q=80'); background-size: cover; background-position: center;"></div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="md:flex items-center justify-between">
            <div class="md:w-1/2 text-center md:text-left mb-8 md:mb-0">
                <span class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm text-white text-xs font-semibold rounded-full mb-3">LIMITED TIME OFFER</span>
                <h2 class="text-4xl md:text-5xl font-extrabold text-white mb-4 leading-tight">
                    Summer Sale is<br><span class="text-yellow-300">Now Live!</span>
                </h2>
                <p class="text-xl text-white text-opacity-90 mb-8 max-w-md">
                    Enjoy up to 50% off on selected items across all categories. Don't miss out on these incredible deals!
                </p>
                <a href="{{ route('shop.index', ['sale' => 'true']) }}" 
                   class="inline-flex items-center px-8 py-4 rounded-lg bg-white text-purple-600 font-bold text-lg hover:bg-yellow-300 hover:text-purple-700 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    Shop Now
                    <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
            <div class="md:w-1/2 flex justify-center">
                <div class="w-64 h-64 sm:w-80 sm:h-80 relative rounded-full bg-white bg-opacity-20 backdrop-blur-sm p-2 transform rotate-3 hover:rotate-6 transition-transform">
                    <div class="w-full h-full rounded-full overflow-hidden border-4 border-white border-opacity-40">
                        <div class="w-full h-full bg-gradient-to-br from-yellow-400 to-pink-500 flex items-center justify-center text-white text-opacity-90 text-center p-6">
                            <div>
                                <p class="text-4xl font-extrabold">50%</p>
                                <p class="text-xl font-bold">OFF</p>
                                <p class="text-sm mt-2 font-medium">Selected Items</p>
                                <p class="text-xs mt-3 font-bold">Use code: SUMMER50</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
