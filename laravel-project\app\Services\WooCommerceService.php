<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class WooCommerceService
{
    protected $baseUrl;
    protected $consumerKey;
    protected $consumerSecret;
    protected $apiVersion;
    protected $config;
    protected $isConfigured = false;

    public function __construct()
    {
        $this->config = [
            'name' => 'Deal4u Store',
            'store_url' => env('WOOCOMMERCE_STORE_URL', 'https://deal4u.co'),
            'consumer_key' => env('WOOCOMMERCE_CONSUMER_KEY', 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193'),
            'consumer_secret' => env('WOOCOMMERCE_CONSUMER_SECRET', 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'),
            'version' => 'wc/v3',
            'timeout' => 60,
        ];

        $this->baseUrl = $this->config['store_url'];
        $this->consumerKey = $this->config['consumer_key'];
        $this->consumerSecret = $this->config['consumer_secret'];
        $this->apiVersion = $this->config['version'];

        $this->isConfigured = $this->isWooCommerceConfigured();
    }

    public function isWooCommerceConfigured(): bool
    {
        return !empty($this->config['store_url']) &&
               !empty($this->config['consumer_key']) &&
               !empty($this->config['consumer_secret']);
    }

    public function isConfigured(): bool
    {
        return $this->isConfigured;
    }

    public function getConfig(): array
    {
        return [
            'name' => $this->config['name'],
            'baseURL' => $this->config['store_url'] . '/wp-json/' . $this->config['version'],
            'hasConsumerKey' => !empty($this->config['consumer_key']),
            'hasConsumerSecret' => !empty($this->config['consumer_secret']),
        ];
    }

    /**
     * Test WooCommerce API connection
     */
    public function testConnection(): array
    {
        if (!$this->isConfigured) {
            return [
                'success' => false,
                'error' => 'WooCommerce not configured properly',
                'config_check' => [
                    'store_url' => !empty($this->config['store_url']),
                    'consumer_key' => !empty($this->config['consumer_key']),
                    'consumer_secret' => !empty($this->config['consumer_secret'])
                ]
            ];
        }

        try {
            $url = $this->config['store_url'] . '/wp-json/' . $this->config['version'] . '/products';

            $response = Http::timeout($this->config['timeout'])
                ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                ->get($url, [
                    'per_page' => 1,
                    'status' => 'publish'
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return [
                    'success' => true,
                    'message' => 'WooCommerce API connection successful',
                    'store_url' => $this->config['store_url'],
                    'api_version' => $this->config['version'],
                    'products_found' => count($data),
                    'total_products' => $response->header('X-WP-Total', 'Unknown'),
                    'sample_product' => $data[0] ?? null,
                    'timestamp' => now()->toISOString()
                ];
            }

            return [
                'success' => false,
                'error' => 'API returned status: ' . $response->status(),
                'response_body' => $response->body(),
                'url' => $url
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'url' => $url ?? 'URL not set',
                'timestamp' => now()->toISOString()
            ];
        }
    }

    /**
     * Get products from WooCommerce with caching and transformation
     */
    public function getProducts($params = [])
    {
        $cacheKey = 'wc_products_' . md5(serialize($params));

        return Cache::remember($cacheKey, 1800, function () use ($params) { // 30 minutes cache
            if (!$this->isConfigured) {
                Log::warning('WooCommerce not configured - returning empty products array');
                return [];
            }

            try {
                $queryParams = array_merge([
                    'per_page' => 50,
                    'page' => 1,
                    'status' => 'publish'
                ], $params);

                Log::info('Fetching products from WooCommerce', ['params' => $queryParams]);

                $response = Http::timeout($this->config['timeout'])
                    ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                    ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . '/products', $queryParams);

                if ($response->successful()) {
                    $products = $response->json();

                    if (!is_array($products)) {
                        Log::error('Invalid response format from WooCommerce API');
                        return [];
                    }

                    Log::info('Successfully fetched ' . count($products) . ' products from WooCommerce');

                    return array_map([$this, 'transformProduct'], $products);
                }

                Log::error('WooCommerce API Error - Products:', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                // Return mock data when API fails
                return $this->getMockProducts($params);
            } catch (\Exception $e) {
                Log::error('WooCommerce API Exception - Products:', ['error' => $e->getMessage()]);
                // Return mock data when API fails
                return $this->getMockProducts($params);
            }
        });
    }

    /**
     * Get a single product by ID
     */
    public function getProduct(int $id): ?array
    {
        $cacheKey = "wc_product_{$id}";

        return Cache::remember($cacheKey, 1800, function () use ($id) {
            if (!$this->isConfigured) {
                return null;
            }

            try {
                $response = Http::timeout($this->config['timeout'])
                    ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                    ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . "/products/{$id}");

                if ($response->successful()) {
                    $product = $response->json();

                    // Fetch variations if they exist
                    if (!empty($product['variations'])) {
                        $variationsResponse = Http::timeout($this->config['timeout'])
                            ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                            ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . "/products/{$id}/variations", [
                                'per_page' => 100
                            ]);

                        if ($variationsResponse->successful()) {
                            $product['variations'] = $variationsResponse->json();
                        }
                    }

                    return $this->transformProduct($product);
                }

                Log::error("Error fetching product {$id}: " . $response->status());
                return null;
            } catch (\Exception $e) {
                Log::error("Error fetching product {$id}: " . $e->getMessage());
                return null;
            }
        });
    }

    /**
     * Search products
     */
    public function searchProducts(string $query): array
    {
        return $this->getProducts(['search' => $query]);
    }

    /**
     * Get orders from WooCommerce
     */
    public function getOrders($params = [])
    {
        try {
            $response = Http::withBasicAuth($this->consumerKey, $this->consumerSecret)
                ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . '/orders', $params);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('WooCommerce API Error - Orders:', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('WooCommerce API Exception - Orders:', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Get customers from WooCommerce
     */
    public function getCustomers($params = [])
    {
        try {
            $response = Http::withBasicAuth($this->consumerKey, $this->consumerSecret)
                ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . '/customers', $params);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error('WooCommerce API Error - Customers:', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('WooCommerce API Exception - Customers:', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Transform WooCommerce product to our format
     */
    public function transformProduct(array $product): array
    {
        $images = $this->processImages($product['images'] ?? [], $product['description'] ?? '');
        $mainImage = $images[0] ?? 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=600&fit=crop';

        $price = (float) ($product['price'] ?? 0);
        $regularPrice = (float) ($product['regular_price'] ?? $price);
        $salePrice = !empty($product['sale_price']) ? (float) $product['sale_price'] : null;

        return [
            'id' => $product['id'],
            'woocommerce_id' => $product['id'],
            'name' => $this->cleanProductText($product['name'] ?? ''),
            'slug' => $product['slug'] ?? '',
            'price' => $price,
            'regular_price' => $regularPrice,
            'sale_price' => $salePrice,
            'image' => $mainImage,
            'images' => $images,
            'category' => $this->cleanProductText($product['categories'][0]['name'] ?? 'Uncategorized'),
            'categories' => array_map(function($cat) {
                return [
                    'id' => $cat['id'],
                    'name' => $this->cleanProductText($cat['name']),
                    'slug' => $cat['slug']
                ];
            }, $product['categories'] ?? []),
            'description' => $this->cleanProductText($product['description'] ?? ''),
            'short_description' => $this->cleanProductText($product['short_description'] ?? ''),
            'stock_status' => $product['stock_status'] ?? 'instock',
            'stock_quantity' => $product['stock_quantity'] ?? 0,
            'in_stock' => ($product['stock_status'] ?? 'instock') === 'instock',
            'sku' => $product['sku'] ?? '',
            'featured' => $product['featured'] ?? false,
            'rating' => (float) ($product['average_rating'] ?? 0),
            'review_count' => (int) ($product['rating_count'] ?? 0),
            'attributes' => array_map(function($attr) {
                return [
                    'name' => $this->cleanProductText($attr['name'] ?? ''),
                    'value' => $this->cleanProductText($attr['value'] ?? ''),
                    'options' => array_map([$this, 'cleanProductText'], $attr['options'] ?? [])
                ];
            }, $product['attributes'] ?? []),
            'variations' => $this->processVariations($product['variations'] ?? [], $product),
            'has_variations' => !empty($product['variations']),
        ];
    }

    /**
     * Clean product text by replacing AliExpress branding with Deal4u
     */
    private function cleanProductText(?string $text): string
    {
        if (empty($text)) {
            return '';
        }

        $replacements = [
            '/AliExpress/i' => 'Deal4u',
            '/Aliexpress/i' => 'Deal4u',
            '/ali express/i' => 'Deal4u',
            '/alibaba/i' => 'Deal4u',
            '/1688\.com/i' => 'deal4u.co',
            '/taobao/i' => 'Deal4u',
        ];

        return preg_replace(array_keys($replacements), array_values($replacements), $text);
    }

    /**
     * Process product images
     */
    private function processImages(array $images, string $description): array
    {
        $processedImages = [];

        foreach ($images as $img) {
            $url = is_string($img) ? $img : ($img['src'] ?? '');
            if ($this->validateImageUrl($url)) {
                $processedImages[] = $url;
            }
        }

        // If no images, extract from description
        if (empty($processedImages) && !empty($description)) {
            $processedImages = $this->extractImagesFromDescription($description);
        }

        // Use placeholder if no images found
        if (empty($processedImages)) {
            $processedImages[] = 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=600&fit=crop';
        }

        return $processedImages;
    }

    /**
     * Process product variations
     */
    private function processVariations(array $variations, array $product): array
    {
        return array_map(function($variant) use ($product) {
            return [
                'id' => $variant['id'],
                'name' => $this->cleanProductText($variant['name'] ?? 'Option'),
                'price' => (float) ($variant['price'] ?? $product['price'] ?? 0),
                'regular_price' => (float) ($variant['regular_price'] ?? $product['regular_price'] ?? 0),
                'sale_price' => !empty($variant['sale_price']) ? (float) $variant['sale_price'] : null,
                'stock_status' => $variant['stock_status'] ?? $product['stock_status'] ?? 'instock',
                'stock_quantity' => $variant['stock_quantity'] ?? 0,
                'image' => $variant['image']['src'] ?? $product['images'][0]['src'] ?? '/placeholder.jpg',
                'attributes' => $variant['attributes'] ?? []
            ];
        }, $variations);
    }

    /**
     * Validate image URL
     */
    private function validateImageUrl(?string $url): bool
    {
        if (empty($url)) {
            return false;
        }

        $cleanUrl = strtok($url, '?'); // Remove query parameters
        return filter_var($cleanUrl, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Extract images from HTML description
     */
    private function extractImagesFromDescription(string $description): array
    {
        $images = [];
        preg_match_all('/<img[^>]+src=["\'](.*?)["\'][^>]*>/i', $description, $matches);

        foreach ($matches[1] as $url) {
            if ($this->validateImageUrl($url) &&
                !str_contains($url, 'placeholder') &&
                !str_contains($url, 'no-image')) {
                $images[] = $url;
            }
        }

        return $images;
    }

    /**
     * Get categories from WooCommerce
     */
    public function getCategories(): array
    {
        $cacheKey = 'wc_categories';

        return Cache::remember($cacheKey, 3600, function () { // 1 hour cache
            if (!$this->isConfigured) {
                Log::warning('WooCommerce not configured - returning empty categories array');
                return [];
            }

            try {
                $response = Http::timeout($this->config['timeout'])
                    ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                    ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . '/products/categories', [
                        'per_page' => 50,
                        'hide_empty' => false,
                        'parent' => 0 // Only top-level categories
                    ]);

                if ($response->successful()) {
                    $categories = $response->json();

                    Log::info('Successfully fetched ' . count($categories) . ' categories from WooCommerce');

                    // Filter categories with products and transform
                    $categoriesWithProducts = array_filter($categories, function($category) {
                        return $category['count'] > 0;
                    });

                    return array_map(function($category) {
                        return [
                            'id' => $category['id'],
                            'name' => $this->cleanProductText($category['name']),
                            'slug' => $category['slug'],
                            'count' => $category['count'],
                            'image' => $category['image']['src'] ?? null,
                            'woocommerce_id' => $category['id']
                        ];
                    }, $categoriesWithProducts);
                }

                Log::error('Error fetching categories from WooCommerce: ' . $response->status());
                return $this->getMockCategories();
            } catch (\Exception $e) {
                Log::error('Error fetching categories from WooCommerce: ' . $e->getMessage());
                return $this->getMockCategories();
            }
        });
    }

    /**
     * Get products by category
     */
    public function getProductsByCategory(string $categorySlug, array $additionalParams = []): array
    {
        try {
            // First get the category ID by slug
            $response = Http::timeout($this->config['timeout'])
                ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                ->get($this->baseUrl . '/wp-json/' . $this->apiVersion . '/products/categories', [
                    'slug' => $categorySlug
                ]);

            if ($response->successful()) {
                $categories = $response->json();

                if (empty($categories)) {
                    return [];
                }

                $categoryId = $categories[0]['id'];
                $params = array_merge($additionalParams, ['category' => $categoryId]);

                return $this->getProducts($params);
            }

            return [];
        } catch (\Exception $e) {
            Log::error('Error fetching products by category: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Create order in WooCommerce
     */
    public function createOrder(array $orderData): ?array
    {
        if (!$this->isConfigured) {
            Log::warning('WooCommerce not configured - creating mock order');
            return [
                'id' => time(),
                'status' => 'processing',
                'total' => $orderData['total'] ?? '0.00',
                'line_items' => $orderData['line_items'] ?? [],
                'billing' => $orderData['billing'] ?? [],
                'shipping' => $orderData['shipping'] ?? []
            ];
        }

        try {
            Log::info('Creating WooCommerce order', [
                'payment_method' => $orderData['payment_method'] ?? 'unknown',
                'total' => $orderData['total'] ?? '0.00',
                'items_count' => count($orderData['line_items'] ?? [])
            ]);

            $response = Http::timeout($this->config['timeout'])
                ->withBasicAuth($this->consumerKey, $this->consumerSecret)
                ->post($this->baseUrl . '/wp-json/' . $this->apiVersion . '/orders', $orderData);

            if ($response->successful()) {
                $order = $response->json();
                Log::info('WooCommerce order created successfully', [
                    'order_id' => $order['id'],
                    'status' => $order['status'],
                    'total' => $order['total']
                ]);
                return $order;
            }

            Log::error('Error creating WooCommerce order: ' . $response->status(), [
                'body' => $response->body()
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Error creating WooCommerce order: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all products with pagination
     */
    public function getAllProducts()
    {
        $allProducts = [];
        $page = 1;
        $perPage = 100;

        do {
            $products = $this->getProducts([
                'per_page' => $perPage,
                'page' => $page
            ]);

            if (!empty($products)) {
                $allProducts = array_merge($allProducts, $products);
                $page++;
            } else {
                break;
            }
        } while (count($products) === $perPage);

        return $allProducts;
    }

    /**
     * Get all orders with pagination
     */
    public function getAllOrders($after = null)
    {
        $allOrders = [];
        $page = 1;
        $perPage = 100;

        $params = [
            'per_page' => $perPage,
            'page' => $page
        ];

        if ($after) {
            $params['after'] = $after;
        }

        do {
            $orders = $this->getOrders($params);

            if (!empty($orders)) {
                $allOrders = array_merge($allOrders, $orders);
                $page++;
                $params['page'] = $page;
            } else {
                break;
            }
        } while (count($orders) === $perPage);

        return $allOrders;
    }

    /**
     * Get mock products when API is not available
     */
    private function getMockProducts($params = []): array
    {
        $perPage = $params['per_page'] ?? 8;
        $featured = isset($params['featured']) && $params['featured'];

        $mockProducts = [
            [
                'id' => 1,
                'woocommerce_id' => 1,
                'name' => 'Premium Wireless Headphones',
                'slug' => 'premium-wireless-headphones',
                'price' => 199.99,
                'regular_price' => 249.99,
                'sale_price' => 199.99,
                'image' => 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&fit=crop',
                'images' => ['https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=600&fit=crop'],
                'category' => 'Electronics',
                'categories' => [['id' => 1, 'name' => 'Electronics', 'slug' => 'electronics']],
                'description' => 'High-quality wireless headphones with noise cancellation.',
                'short_description' => 'Premium wireless headphones',
                'stock_status' => 'instock',
                'stock_quantity' => 50,
                'in_stock' => true,
                'sku' => 'PWH-001',
                'featured' => true,
                'rating' => 4.5,
                'review_count' => 128,
                'attributes' => [],
                'variations' => [],
                'has_variations' => false,
            ],
            [
                'id' => 2,
                'woocommerce_id' => 2,
                'name' => 'Smart Fitness Watch',
                'slug' => 'smart-fitness-watch',
                'price' => 299.99,
                'regular_price' => 299.99,
                'sale_price' => null,
                'image' => 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=600&fit=crop',
                'images' => ['https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=600&h=600&fit=crop'],
                'category' => 'Electronics',
                'categories' => [['id' => 1, 'name' => 'Electronics', 'slug' => 'electronics']],
                'description' => 'Advanced fitness tracking with heart rate monitoring.',
                'short_description' => 'Smart fitness watch',
                'stock_status' => 'instock',
                'stock_quantity' => 25,
                'in_stock' => true,
                'sku' => 'SFW-002',
                'featured' => false,
                'rating' => 4.2,
                'review_count' => 89,
                'attributes' => [],
                'variations' => [],
                'has_variations' => false,
            ],
            [
                'id' => 3,
                'woocommerce_id' => 3,
                'name' => 'Portable Bluetooth Speaker',
                'slug' => 'portable-bluetooth-speaker',
                'price' => 79.99,
                'regular_price' => 99.99,
                'sale_price' => 79.99,
                'image' => 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&h=600&fit=crop',
                'images' => ['https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=600&h=600&fit=crop'],
                'category' => 'Electronics',
                'categories' => [['id' => 1, 'name' => 'Electronics', 'slug' => 'electronics']],
                'description' => 'Compact speaker with powerful sound and long battery life.',
                'short_description' => 'Portable Bluetooth speaker',
                'stock_status' => 'instock',
                'stock_quantity' => 75,
                'in_stock' => true,
                'sku' => 'PBS-003',
                'featured' => true,
                'rating' => 4.7,
                'review_count' => 156,
                'attributes' => [],
                'variations' => [],
                'has_variations' => false,
            ],
            [
                'id' => 4,
                'woocommerce_id' => 4,
                'name' => 'Wireless Charging Pad',
                'slug' => 'wireless-charging-pad',
                'price' => 39.99,
                'regular_price' => 39.99,
                'sale_price' => null,
                'image' => 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&h=600&fit=crop',
                'images' => ['https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=600&h=600&fit=crop'],
                'category' => 'Electronics',
                'categories' => [['id' => 1, 'name' => 'Electronics', 'slug' => 'electronics']],
                'description' => 'Fast wireless charging for compatible devices.',
                'short_description' => 'Wireless charging pad',
                'stock_status' => 'instock',
                'stock_quantity' => 100,
                'in_stock' => true,
                'sku' => 'WCP-004',
                'featured' => false,
                'rating' => 4.0,
                'review_count' => 67,
                'attributes' => [],
                'variations' => [],
                'has_variations' => false,
            ]
        ];

        // Filter by featured if requested
        if ($featured) {
            $mockProducts = array_filter($mockProducts, function($product) {
                return $product['featured'];
            });
        }

        // Limit results
        return array_slice($mockProducts, 0, $perPage);
    }

    /**
     * Get mock categories when API is not available
     */
    private function getMockCategories(): array
    {
        return [
            [
                'id' => 1,
                'name' => 'Electronics',
                'slug' => 'electronics',
                'count' => 25,
                'image' => null,
                'woocommerce_id' => 1
            ],
            [
                'id' => 2,
                'name' => 'Fashion',
                'slug' => 'fashion',
                'count' => 18,
                'image' => null,
                'woocommerce_id' => 2
            ],
            [
                'id' => 3,
                'name' => 'Home & Garden',
                'slug' => 'home-garden',
                'count' => 12,
                'image' => null,
                'woocommerce_id' => 3
            ],
            [
                'id' => 4,
                'name' => 'Sports & Outdoors',
                'slug' => 'sports-outdoors',
                'count' => 15,
                'image' => null,
                'woocommerce_id' => 4
            ]
        ];
    }
}