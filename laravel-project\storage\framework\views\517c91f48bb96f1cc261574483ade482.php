<?php $__env->startSection('title', 'Shopping Cart - Deal4u'); ?>
<?php $__env->startSection('description', 'Review your selected items and proceed to checkout. Secure payment and fast shipping guaranteed.'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
            <p class="mt-2 text-gray-600">Review your items and proceed to checkout</p>
        </div>

        <?php if($cartSummary['item_count'] > 0): ?>
            <div class="lg:grid lg:grid-cols-12 lg:gap-8">
                <!-- Cart Items -->
                <div class="lg:col-span-8">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-lg font-medium text-gray-900">
                                Cart Items (<?php echo e($cartSummary['item_count']); ?>)
                            </h2>
                        </div>
                        
                        <div class="divide-y divide-gray-200">
                            <?php $__currentLoopData = $cartSummary['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="p-6 flex items-center space-x-4" id="cart-item-<?php echo e($item->product_id); ?>">
                                <!-- Product Image -->
                                <div class="flex-shrink-0">
                                    <img src="<?php echo e($item->product['image']); ?>" 
                                         alt="<?php echo e($item->product['name']); ?>" 
                                         class="w-20 h-20 object-cover rounded-lg">
                                </div>
                                
                                <!-- Product Details -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-medium text-gray-900">
                                        <a href="<?php echo e(route('product.show', $item->product_id)); ?>" 
                                           class="hover:text-blue-600 transition-colors">
                                            <?php echo e($item->product['name']); ?>

                                        </a>
                                    </h3>
                                    
                                    <?php if(isset($item->product['category']) && $item->product['category']): ?>
                                    <p class="text-sm text-gray-500 mt-1"><?php echo e($item->product['category']); ?></p>
                                    <?php endif; ?>
                                    
                                    <?php if(isset($item->product['sku']) && $item->product['sku']): ?>
                                    <p class="text-xs text-gray-400 mt-1">SKU: <?php echo e($item->product['sku']); ?></p>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Quantity Controls -->
                                <div class="flex items-center space-x-2">
                                    <button onclick="updateCartQuantity(<?php echo e($item->product_id); ?>, <?php echo e($item->quantity - 1); ?>)" 
                                            class="p-1 text-gray-400 hover:text-gray-600 <?php echo e($item->quantity <= 1 ? 'opacity-50 cursor-not-allowed' : ''); ?>"
                                            <?php echo e($item->quantity <= 1 ? 'disabled' : ''); ?>>
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                        </svg>
                                    </button>
                                    
                                    <span class="w-8 text-center font-medium" id="quantity-<?php echo e($item->product_id); ?>">
                                        <?php echo e($item->quantity); ?>

                                    </span>
                                    
                                    <button onclick="updateCartQuantity(<?php echo e($item->product_id); ?>, <?php echo e($item->quantity + 1); ?>)" 
                                            class="p-1 text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </button>
                                </div>
                                
                                <!-- Price -->
                                <div class="text-right">
                                    <?php
                                        $price = $item->product['sale_price'] ?? $item->product['price'] ?? $item->product['regular_price'] ?? 0;
                                        $total = $price * $item->quantity;
                                    ?>
                                    
                                    <?php if(isset($item->product['sale_price']) && $item->product['sale_price'] && $item->product['sale_price'] < $item->product['regular_price']): ?>
                                        <div class="text-sm text-gray-500 line-through">
                                            $<?php echo e(number_format($item->product['regular_price'] * $item->quantity, 2)); ?>

                                        </div>
                                        <div class="text-lg font-medium text-red-600" id="total-<?php echo e($item->product_id); ?>">
                                            $<?php echo e(number_format($total, 2)); ?>

                                        </div>
                                    <?php else: ?>
                                        <div class="text-lg font-medium text-gray-900" id="total-<?php echo e($item->product_id); ?>">
                                            $<?php echo e(number_format($total, 2)); ?>

                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="text-sm text-gray-500">
                                        $<?php echo e(number_format($price, 2)); ?> each
                                    </div>
                                </div>
                                
                                <!-- Remove Button -->
                                <div>
                                    <button onclick="removeFromCart(<?php echo e($item->product_id); ?>)" 
                                            class="p-2 text-red-400 hover:text-red-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <!-- Cart Actions -->
                        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <button onclick="clearCart()" 
                                        class="text-red-600 hover:text-red-800 font-medium text-sm transition-colors">
                                    Clear Cart
                                </button>
                                <a href="<?php echo e(route('shop.index')); ?>" 
                                   class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                                    Continue Shopping
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Order Summary -->
                <div class="lg:col-span-4 mt-8 lg:mt-0">
                    <div class="bg-white rounded-lg shadow-sm p-6 sticky top-24">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Order Summary</h2>
                        
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Subtotal (<?php echo e($cartSummary['item_count']); ?> items)</span>
                                <span class="font-medium" id="cart-subtotal">$<?php echo e(number_format($cartSummary['subtotal'], 2)); ?></span>
                            </div>
                            
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Shipping</span>
                                <span class="font-medium" id="cart-shipping">
                                    <?php if($cartSummary['shipping'] > 0): ?>
                                        $<?php echo e(number_format($cartSummary['shipping'], 2)); ?>

                                    <?php else: ?>
                                        <span class="text-green-600">Free</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Tax</span>
                                <span class="font-medium" id="cart-tax">$<?php echo e(number_format($cartSummary['tax'], 2)); ?></span>
                            </div>
                            
                            <div class="border-t border-gray-200 pt-3">
                                <div class="flex justify-between">
                                    <span class="text-lg font-medium text-gray-900">Total</span>
                                    <span class="text-lg font-bold text-gray-900" id="cart-total">
                                        $<?php echo e(number_format($cartSummary['total'], 2)); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <?php if($cartSummary['shipping'] > 0): ?>
                        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                            <p class="text-sm text-blue-800">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Add $<?php echo e(number_format(50 - $cartSummary['subtotal'], 2)); ?> more for free shipping!
                            </p>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-6 space-y-3">
                            <a href="<?php echo e(route('cart.checkout')); ?>" 
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg text-center block transition-colors">
                                Proceed to Checkout
                            </a>
                            
                            <button class="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium py-3 px-4 rounded-lg transition-colors">
                                Pay with PayPal
                            </button>
                        </div>
                        
                        <!-- Security Features -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                    Secure Checkout
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    SSL Protected
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Empty Cart -->
            <div class="text-center py-12">
                <div class="bg-white rounded-lg shadow-sm p-12">
                    <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4.5M9 7h6"></path>
                    </svg>
                    <h3 class="text-2xl font-medium text-gray-900 mb-2">Your cart is empty</h3>
                    <p class="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
                    <a href="<?php echo e(route('shop.index')); ?>" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        Start Shopping
                        <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function updateCartQuantity(productId, newQuantity) {
    if (newQuantity < 1) return;
    
    fetch('/api/v1/cart/update', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: newQuantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update quantity display
            document.getElementById(`quantity-${productId}`).textContent = newQuantity;
            
            // Refresh cart summary
            refreshCartSummary();
            updateCartCount();
            
            showNotification('Cart updated successfully', 'success');
        } else {
            showNotification(data.message || 'Error updating cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating cart', 'error');
    });
}

function removeFromCart(productId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    fetch('/api/v1/cart/remove', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove item from DOM
            document.getElementById(`cart-item-${productId}`).remove();
            
            // Refresh cart summary
            refreshCartSummary();
            updateCartCount();
            
            showNotification('Item removed from cart', 'success');
            
            // Reload page if cart is empty
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Error removing item', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error removing item', 'error');
    });
}

function clearCart() {
    if (!confirm('Are you sure you want to clear your entire cart?')) {
        return;
    }
    
    fetch('/api/v1/cart/clear', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Cart cleared successfully', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Error clearing cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error clearing cart', 'error');
    });
}

function refreshCartSummary() {
    fetch('/api/v1/cart/summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cart = data.cart;
                document.getElementById('cart-subtotal').textContent = `$${cart.subtotal.toFixed(2)}`;
                document.getElementById('cart-tax').textContent = `$${cart.tax.toFixed(2)}`;
                document.getElementById('cart-total').textContent = `$${cart.total.toFixed(2)}`;
                
                const shippingElement = document.getElementById('cart-shipping');
                if (cart.shipping > 0) {
                    shippingElement.innerHTML = `$${cart.shipping.toFixed(2)}`;
                } else {
                    shippingElement.innerHTML = '<span class="text-green-600">Free</span>';
                }
            }
        })
        .catch(error => console.error('Error refreshing cart summary:', error));
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/cart/index.blade.php ENDPATH**/ ?>