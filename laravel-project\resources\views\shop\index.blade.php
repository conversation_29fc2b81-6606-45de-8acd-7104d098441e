@extends('layouts.app')

@section('title', 'Shop - Deal4u')
@section('description', 'Browse our extensive collection of premium products at unbeatable prices. Find exactly what you\'re looking for with our advanced search and filtering options.')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-4xl font-bold mb-2">Shop</h1>
                    <p class="text-xl text-blue-100">Discover amazing gaming consoles and tech products</p>
                    <div class="mt-4 flex items-center text-blue-200">
                        <span class="text-sm">Showing {{ count($products) }} products</span>
                    </div>
                </div>
                
                <!-- Search and Sort -->
                <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-4">
                    <!-- Search Form -->
                    <form action="{{ route('shop.index') }}" method="GET" class="flex">
                        <input type="hidden" name="category" value="{{ request('category') }}">
                        <input type="hidden" name="min_price" value="{{ request('min_price') }}">
                        <input type="hidden" name="max_price" value="{{ request('max_price') }}">
                        <input type="hidden" name="sale" value="{{ request('sale') }}">
                        <input type="hidden" name="orderby" value="{{ request('orderby') }}">
                        
                        <div class="relative">
                            <input type="text"
                                   name="search"
                                   value="{{ request('search') }}"
                                   placeholder="Search gaming consoles, electronics..."
                                   class="w-full md:w-80 pl-10 pr-4 py-3 border-2 border-white border-opacity-30 rounded-l-xl bg-white bg-opacity-20 backdrop-blur-sm text-white placeholder-blue-200 focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 focus:bg-opacity-30">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <button type="submit" class="px-6 py-3 bg-yellow-400 text-purple-900 rounded-r-xl hover:bg-yellow-300 transition-colors font-semibold">
                            Search
                        </button>
                    </form>
                    
                    <!-- Sort Dropdown -->
                    <form action="{{ route('shop.index') }}" method="GET" id="sortForm">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        <input type="hidden" name="category" value="{{ request('category') }}">
                        <input type="hidden" name="min_price" value="{{ request('min_price') }}">
                        <input type="hidden" name="max_price" value="{{ request('max_price') }}">
                        <input type="hidden" name="sale" value="{{ request('sale') }}">
                        
                        <select name="orderby" onchange="document.getElementById('sortForm').submit()"
                                class="border-2 border-white border-opacity-30 rounded-xl px-4 py-3 bg-white bg-opacity-20 backdrop-blur-sm text-white focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 focus:bg-opacity-30">
                            <option value="" class="text-gray-900">Sort by</option>
                            <option value="date" {{ request('orderby') == 'date' ? 'selected' : '' }} class="text-gray-900">Newest</option>
                            <option value="popularity" {{ request('orderby') == 'popularity' ? 'selected' : '' }} class="text-gray-900">Popularity</option>
                            <option value="price" {{ request('orderby') == 'price' ? 'selected' : '' }} class="text-gray-900">Price: Low to High</option>
                            <option value="price-desc" {{ request('orderby') == 'price-desc' ? 'selected' : '' }} class="text-gray-900">Price: High to Low</option>
                            <option value="rating" {{ request('orderby') == 'rating' ? 'selected' : '' }} class="text-gray-900">Rating</option>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="lg:grid lg:grid-cols-4 lg:gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-24">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                    
                    <form action="{{ route('shop.index') }}" method="GET" id="filterForm">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        <input type="hidden" name="orderby" value="{{ request('orderby') }}">
                        
                        <!-- Categories Filter -->
                        @if(count($categories) > 0)
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Categories</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="" 
                                           {{ !request('category') ? 'checked' : '' }}
                                           onchange="document.getElementById('filterForm').submit()"
                                           class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">All Categories</span>
                                </label>
                                @foreach($categories as $category)
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="{{ $category['id'] }}" 
                                           {{ request('category') == $category['id'] ? 'checked' : '' }}
                                           onchange="document.getElementById('filterForm').submit()"
                                           class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">{{ $category['name'] }} ({{ $category['count'] }})</span>
                                </label>
                                @endforeach
                            </div>
                        </div>
                        @endif
                        
                        <!-- Price Range Filter -->
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Price Range</h4>
                            <div class="flex items-center space-x-2">
                                <input type="number" 
                                       name="min_price" 
                                       value="{{ request('min_price') }}" 
                                       placeholder="Min" 
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="text-gray-500">-</span>
                                <input type="number" 
                                       name="max_price" 
                                       value="{{ request('max_price') }}" 
                                       placeholder="Max" 
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="submit" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                    Apply
                                </button>
                            </div>
                        </div>
                        
                        <!-- Sale Items Filter -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" 
                                       name="sale" 
                                       value="true" 
                                       {{ request('sale') == 'true' ? 'checked' : '' }}
                                       onchange="document.getElementById('filterForm').submit()"
                                       class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Sale Items Only</span>
                            </label>
                        </div>
                        
                        <!-- Clear Filters -->
                        @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale', 'orderby']))
                        <div class="pt-4 border-t border-gray-200">
                            <a href="{{ route('shop.index') }}" 
                               class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                Clear All Filters
                            </a>
                        </div>
                        @endif
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3 mt-8 lg:mt-0">
                <!-- Active Filters Display -->
                @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                <div class="mb-6 flex flex-wrap gap-2">
                    @if(request('search'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                            Search: "{{ request('search') }}"
                            <a href="{{ route('shop.index', array_filter(request()->except('search'))) }}" class="ml-2 text-blue-600 hover:text-blue-800">×</a>
                        </span>
                    @endif
                    
                    @if(request('category'))
                        @php
                            $selectedCategory = collect($categories)->firstWhere('id', request('category'));
                        @endphp
                        @if($selectedCategory)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                            Category: {{ $selectedCategory['name'] }}
                            <a href="{{ route('shop.index', array_filter(request()->except('category'))) }}" class="ml-2 text-green-600 hover:text-green-800">×</a>
                        </span>
                        @endif
                    @endif
                    
                    @if(request('min_price') || request('max_price'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                            Price: ${{ request('min_price', '0') }} - ${{ request('max_price', '∞') }}
                            <a href="{{ route('shop.index', array_filter(request()->except(['min_price', 'max_price']))) }}" class="ml-2 text-purple-600 hover:text-purple-800">×</a>
                        </span>
                    @endif
                    
                    @if(request('sale') == 'true')
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-red-100 text-red-800">
                            Sale Items
                            <a href="{{ route('shop.index', array_filter(request()->except('sale'))) }}" class="ml-2 text-red-600 hover:text-red-800">×</a>
                        </span>
                    @endif
                </div>
                @endif
                
                <!-- Products Count -->
                <div class="flex items-center justify-between mb-6">
                    <p class="text-sm text-gray-600">
                        Showing {{ count($products) }} products
                        @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                            (filtered)
                        @endif
                    </p>
                </div>
                
                <!-- Products Grid -->
                @if(count($products) > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach($products as $product)
                            <div class="animate-fade-in-up" style="animation-delay: {{ $loop->index * 0.1 }}s;">
                                @include('components.product-card', ['product' => $product])
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Load More Button (if needed) -->
                    @if(count($products) >= 24)
                    <div class="text-center mt-12">
                        <button onclick="loadMoreProducts()" 
                                class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            Load More Products
                            <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    @endif
                @else
                    <!-- No Products Found -->
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">No products found</h3>
                        <p class="mt-2 text-gray-600">
                            @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                                Try adjusting your filters or search terms.
                            @else
                                Please check back later for new products.
                            @endif
                        </p>
                        @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                        <div class="mt-6">
                            <a href="{{ route('shop.index') }}" 
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Clear All Filters
                            </a>
                        </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function loadMoreProducts() {
    // This would implement pagination/infinite scroll
    // For now, just show a message
    showNotification('Load more functionality would be implemented here', 'info');
}
</script>
@endpush
@endsection
