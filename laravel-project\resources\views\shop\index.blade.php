@extends('layouts.app')

@section('title', 'Shop - Deal4u')
@section('description', 'Browse our extensive collection of premium products at unbeatable prices. Find exactly what you\'re looking for with our advanced search and filtering options.')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); background-size: 30px 30px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
            <div class="text-center mb-8">
                <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-4">
                    <span class="text-xs font-semibold tracking-wider">🛍️ PREMIUM PRODUCTS 🛍️</span>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    Shop Our Collection
                </h1>
                <p class="text-xl text-blue-100 max-w-2xl mx-auto mb-6">
                    Discover amazing gaming consoles, electronics, and tech products at unbeatable prices
                </p>
                <div class="flex items-center justify-center text-blue-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <span class="text-sm">Showing {{ count($products) }} premium products</span>
                </div>
            </div>
                
                <!-- Search and Sort -->
                <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-4">
                    <!-- Search Form -->
                    <form action="{{ route('shop.index') }}" method="GET" class="flex">
                        <input type="hidden" name="category" value="{{ request('category') }}">
                        <input type="hidden" name="min_price" value="{{ request('min_price') }}">
                        <input type="hidden" name="max_price" value="{{ request('max_price') }}">
                        <input type="hidden" name="sale" value="{{ request('sale') }}">
                        <input type="hidden" name="orderby" value="{{ request('orderby') }}">
                        
                        <div class="relative">
                            <input type="text"
                                   name="search"
                                   value="{{ request('search') }}"
                                   placeholder="Search gaming consoles, electronics..."
                                   class="w-full md:w-80 pl-10 pr-4 py-3 border-2 border-white border-opacity-30 rounded-l-xl bg-white bg-opacity-20 backdrop-blur-sm text-white placeholder-blue-200 focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 focus:bg-opacity-30">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <button type="submit" class="px-6 py-3 bg-yellow-400 text-purple-900 rounded-r-xl hover:bg-yellow-300 transition-colors font-semibold">
                            Search
                        </button>
                    </form>
                    
                    <!-- Sort Dropdown -->
                    <form action="{{ route('shop.index') }}" method="GET" id="sortForm">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        <input type="hidden" name="category" value="{{ request('category') }}">
                        <input type="hidden" name="min_price" value="{{ request('min_price') }}">
                        <input type="hidden" name="max_price" value="{{ request('max_price') }}">
                        <input type="hidden" name="sale" value="{{ request('sale') }}">
                        
                        <select name="orderby" onchange="document.getElementById('sortForm').submit()"
                                class="border-2 border-white border-opacity-30 rounded-xl px-4 py-3 bg-white bg-opacity-20 backdrop-blur-sm text-white focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 focus:bg-opacity-30">
                            <option value="" class="text-gray-900">Sort by</option>
                            <option value="date" {{ request('orderby') == 'date' ? 'selected' : '' }} class="text-gray-900">Newest</option>
                            <option value="popularity" {{ request('orderby') == 'popularity' ? 'selected' : '' }} class="text-gray-900">Popularity</option>
                            <option value="price" {{ request('orderby') == 'price' ? 'selected' : '' }} class="text-gray-900">Price: Low to High</option>
                            <option value="price-desc" {{ request('orderby') == 'price-desc' ? 'selected' : '' }} class="text-gray-900">Price: High to Low</option>
                            <option value="rating" {{ request('orderby') == 'rating' ? 'selected' : '' }} class="text-gray-900">Rating</option>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="lg:grid lg:grid-cols-4 lg:gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-lg p-6 sticky top-24 border border-gray-100">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
                        @if(request()->hasAny(['category', 'min_price', 'max_price', 'sale']))
                            <button onclick="clearAllFilters()"
                                    class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                Clear All
                            </button>
                        @endif
                    </div>
                    
                    <form action="{{ route('shop.index') }}" method="GET" id="filterForm">
                        <input type="hidden" name="search" value="{{ request('search') }}">
                        <input type="hidden" name="orderby" value="{{ request('orderby') }}">
                        
                        <!-- Categories Filter -->
                        @if(count($categories) > 0)
                        <div class="mb-6">
                            <h4 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                Categories
                            </h4>
                            <div class="space-y-3">
                                <label class="flex items-center p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                                    <input type="radio" name="category" value=""
                                           {{ !request('category') ? 'checked' : '' }}
                                           onchange="applyFilter()"
                                           class="text-blue-600 focus:ring-blue-500 focus:ring-2">
                                    <span class="ml-3 text-sm text-gray-700 font-medium">All Categories</span>
                                </label>
                                @foreach($categories as $category)
                                <label class="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                                    <div class="flex items-center">
                                        <input type="radio" name="category" value="{{ $category['id'] }}"
                                               {{ request('category') == $category['id'] ? 'checked' : '' }}
                                               onchange="applyFilter()"
                                               class="text-blue-600 focus:ring-blue-500 focus:ring-2">
                                        <span class="ml-3 text-sm text-gray-700">{{ $category['name'] }}</span>
                                    </div>
                                    <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{{ $category['count'] }}</span>
                                </label>
                                @endforeach
                            </div>
                        </div>
                        @endif
                        
                        <!-- Price Range Filter -->
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Price Range</h4>
                            <div class="flex items-center space-x-2">
                                <input type="number" 
                                       name="min_price" 
                                       value="{{ request('min_price') }}" 
                                       placeholder="Min" 
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="text-gray-500">-</span>
                                <input type="number" 
                                       name="max_price" 
                                       value="{{ request('max_price') }}" 
                                       placeholder="Max" 
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="submit" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                    Apply
                                </button>
                            </div>
                        </div>
                        
                        <!-- Sale Items Filter -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" 
                                       name="sale" 
                                       value="true" 
                                       {{ request('sale') == 'true' ? 'checked' : '' }}
                                       onchange="document.getElementById('filterForm').submit()"
                                       class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Sale Items Only</span>
                            </label>
                        </div>
                        
                        <!-- Clear Filters -->
                        @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale', 'orderby']))
                        <div class="pt-4 border-t border-gray-200">
                            <a href="{{ route('shop.index') }}" 
                               class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                Clear All Filters
                            </a>
                        </div>
                        @endif
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3 mt-8 lg:mt-0">
                <!-- Active Filters Display -->
                @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                <div class="mb-6 flex flex-wrap gap-2">
                    @if(request('search'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                            Search: "{{ request('search') }}"
                            <a href="{{ route('shop.index', array_filter(request()->except('search'))) }}" class="ml-2 text-blue-600 hover:text-blue-800">×</a>
                        </span>
                    @endif
                    
                    @if(request('category'))
                        @php
                            $selectedCategory = collect($categories)->firstWhere('id', request('category'));
                        @endphp
                        @if($selectedCategory)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                            Category: {{ $selectedCategory['name'] }}
                            <a href="{{ route('shop.index', array_filter(request()->except('category'))) }}" class="ml-2 text-green-600 hover:text-green-800">×</a>
                        </span>
                        @endif
                    @endif
                    
                    @if(request('min_price') || request('max_price'))
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                            Price: ${{ request('min_price', '0') }} - ${{ request('max_price', '∞') }}
                            <a href="{{ route('shop.index', array_filter(request()->except(['min_price', 'max_price']))) }}" class="ml-2 text-purple-600 hover:text-purple-800">×</a>
                        </span>
                    @endif
                    
                    @if(request('sale') == 'true')
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-red-100 text-red-800">
                            Sale Items
                            <a href="{{ route('shop.index', array_filter(request()->except('sale'))) }}" class="ml-2 text-red-600 hover:text-red-800">×</a>
                        </span>
                    @endif
                </div>
                @endif
                
                <!-- Products Count -->
                <div class="flex items-center justify-between mb-6">
                    <p class="text-sm text-gray-600">
                        Showing {{ count($products) }} products
                        @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                            (filtered)
                        @endif
                    </p>
                </div>
                
                <!-- Loading Skeleton (hidden by default, shown via JavaScript) -->
                <div id="products-loading" class="hidden grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    @for($i = 0; $i < 8; $i++)
                        @include('components.product-skeleton')
                    @endfor
                </div>

                <!-- Products Grid -->
                @if(count($products) > 0)
                    <div id="products-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach($products as $product)
                            <div class="animate-fade-in-up" style="animation-delay: {{ $loop->index * 0.1 }}s;">
                                @include('components.product-card', ['product' => $product])
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Load More Button (if needed) -->
                    @if(count($products) >= 24)
                    <div class="text-center mt-12">
                        <button onclick="loadMoreProducts()" 
                                class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            Load More Products
                            <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    @endif
                @else
                    <!-- No Products Found -->
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">No products found</h3>
                        <p class="mt-2 text-gray-600">
                            @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                                Try adjusting your filters or search terms.
                            @else
                                Please check back later for new products.
                            @endif
                        </p>
                        @if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale']))
                        <div class="mt-6">
                            <a href="{{ route('shop.index') }}" 
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Clear All Filters
                            </a>
                        </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Apply filters with loading state
function applyFilter() {
    showLoading();
    setTimeout(() => {
        document.getElementById('filterForm').submit();
    }, 300);
}

// Clear all filters
function clearAllFilters() {
    showLoading();
    window.location.href = '{{ route("shop.index") }}';
}

// Show loading skeleton
function showLoading() {
    const productsGrid = document.getElementById('products-grid');
    const loadingSkeleton = document.getElementById('products-loading');

    if (productsGrid) productsGrid.style.display = 'none';
    if (loadingSkeleton) loadingSkeleton.classList.remove('hidden');
}

function loadMoreProducts() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '<svg class="animate-spin h-5 w-5 mx-auto" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
    button.disabled = true;

    // Simulate loading more products
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        // Here you would typically load more products via AJAX
    }, 2000);
}

// Price range filter with debounce
let priceTimeout;
function handlePriceChange() {
    clearTimeout(priceTimeout);
    priceTimeout = setTimeout(() => {
        applyFilter();
    }, 1000);
}

// Add event listeners for price inputs
document.addEventListener('DOMContentLoaded', function() {
    const minPrice = document.querySelector('input[name="min_price"]');
    const maxPrice = document.querySelector('input[name="max_price"]');

    if (minPrice) minPrice.addEventListener('input', handlePriceChange);
    if (maxPrice) maxPrice.addEventListener('input', handlePriceChange);
});
</script>
@endpush
@endsection
