<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\WooCommerceService;

class OrderController extends Controller
{
    protected $wooCommerceService;

    public function __construct(WooCommerceService $wooCommerceService)
    {
        $this->wooCommerceService = $wooCommerceService;
    }

    public function track(Request $request)
    {
        return view('orders.track');
    }

    public function trackOrder(Request $request)
    {
        $request->validate([
            'order_id' => 'required|string',
            'email' => 'required|email'
        ]);

        try {
            // Try to get order from WooCommerce
            $order = $this->wooCommerceService->getOrder($request->order_id);
            
            if ($order && isset($order['billing']['email']) && 
                strtolower($order['billing']['email']) === strtolower($request->email)) {
                
                return view('orders.details', compact('order'));
            }
        } catch (\Exception $e) {
            // If WooCommerce fails, show demo order
        }

        // Demo order for testing
        if (str_contains($request->order_id, 'demo') || str_contains($request->order_id, 'test')) {
            $order = $this->getDemoOrder($request->order_id);
            return view('orders.details', compact('order'));
        }

        return redirect()->back()->with('error', 'Order not found. Please check your order ID and email address.');
    }

    private function getDemoOrder($orderId)
    {
        return [
            'id' => $orderId,
            'number' => $orderId,
            'status' => 'processing',
            'date_created' => now()->subDays(2)->toISOString(),
            'total' => '89.99',
            'currency' => 'EUR',
            'billing' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'address_1' => '123 Main Street',
                'city' => 'New York',
                'postcode' => '10001',
                'country' => 'US'
            ],
            'shipping' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'address_1' => '123 Main Street',
                'city' => 'New York',
                'postcode' => '10001',
                'country' => 'US'
            ],
            'line_items' => [
                [
                    'id' => 1,
                    'name' => 'Premium Wireless Headphones',
                    'quantity' => 1,
                    'price' => 79.99,
                    'total' => '79.99'
                ],
                [
                    'id' => 2,
                    'name' => 'Phone Case',
                    'quantity' => 1,
                    'price' => 10.00,
                    'total' => '10.00'
                ]
            ],
            'shipping_lines' => [
                [
                    'method_title' => 'Standard Shipping',
                    'total' => '0.00'
                ]
            ],
            'tracking_number' => 'TRK' . strtoupper(substr($orderId, -8)),
            'estimated_delivery' => now()->addDays(3)->format('Y-m-d'),
            'tracking_url' => 'https://tracking.example.com/track/' . 'TRK' . strtoupper(substr($orderId, -8))
        ];
    }

    public function history()
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please login to view your order history.');
        }

        // In a real application, you would fetch orders from the database or WooCommerce
        $orders = $this->getDemoOrderHistory();

        return view('orders.history', compact('orders'));
    }

    private function getDemoOrderHistory()
    {
        return [
            [
                'id' => 'ORD-2024-001',
                'number' => 'ORD-2024-001',
                'status' => 'completed',
                'date_created' => now()->subDays(10)->format('Y-m-d H:i:s'),
                'total' => '129.99',
                'currency' => 'EUR',
                'items_count' => 2
            ],
            [
                'id' => 'ORD-2024-002',
                'number' => 'ORD-2024-002',
                'status' => 'processing',
                'date_created' => now()->subDays(3)->format('Y-m-d H:i:s'),
                'total' => '89.99',
                'currency' => 'EUR',
                'items_count' => 1
            ],
            [
                'id' => 'ORD-2024-003',
                'number' => 'ORD-2024-003',
                'status' => 'shipped',
                'date_created' => now()->subDays(1)->format('Y-m-d H:i:s'),
                'total' => '45.50',
                'currency' => 'EUR',
                'items_count' => 3
            ]
        ];
    }
}
