<?php $__env->startSection('title', 'API Connection Status'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">WooCommerce API Status</h1>
            <p class="text-gray-600 mt-2">Current connection status and configuration details</p>
        </div>

        <!-- Connection Status Card -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Connection Status</h2>
                <button onclick="location.reload()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Refresh Status
                </button>
            </div>

            <?php
                $wooService = app(\App\Services\WooCommerceService::class);
                $connectionTest = $wooService->testConnection();
                $config = $wooService->getConfig();
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Status Overview -->
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <?php if($connectionTest['success']): ?>
                            <div class="flex-shrink-0 w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-green-700 font-medium">API Connected</span>
                        <?php else: ?>
                            <div class="flex-shrink-0 w-3 h-3 bg-red-500 rounded-full"></div>
                            <span class="text-red-700 font-medium">API Disconnected</span>
                        <?php endif; ?>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Current Mode</h3>
                        <?php if($connectionTest['success']): ?>
                            <p class="text-sm text-green-600">✅ Live WooCommerce Data</p>
                        <?php else: ?>
                            <p class="text-sm text-orange-600">🔄 Mock Data Fallback</p>
                            <p class="text-xs text-gray-500 mt-1">Application continues to work with sample data</p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Configuration Details -->
                <div class="space-y-4">
                    <h3 class="font-medium text-gray-900">Configuration</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Store URL:</span>
                            <span class="font-mono text-gray-900"><?php echo e($config['store_url'] ?? 'Not set'); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">API Version:</span>
                            <span class="font-mono text-gray-900"><?php echo e($config['version'] ?? 'Not set'); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Consumer Key:</span>
                            <span class="font-mono text-gray-900">
                                <?php if(!empty($config['consumer_key'])): ?>
                                    <?php echo e(substr($config['consumer_key'], 0, 10)); ?>...
                                <?php else: ?>
                                    Not set
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Consumer Secret:</span>
                            <span class="font-mono text-gray-900">
                                <?php if(!empty($config['consumer_secret'])): ?>
                                    <?php echo e(substr($config['consumer_secret'], 0, 10)); ?>...
                                <?php else: ?>
                                    Not set
                                <?php endif; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Details (if any) -->
        <?php if(!$connectionTest['success']): ?>
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h3 class="text-lg font-semibold text-red-900 mb-3">Connection Issues</h3>
            
            <div class="space-y-3">
                <div>
                    <h4 class="font-medium text-red-800">Error Message:</h4>
                    <p class="text-red-700 text-sm mt-1"><?php echo e($connectionTest['error'] ?? 'Unknown error'); ?></p>
                </div>

                <?php if(isset($connectionTest['response_body'])): ?>
                <div>
                    <h4 class="font-medium text-red-800">API Response:</h4>
                    <pre class="text-red-700 text-xs mt-1 bg-red-100 p-2 rounded overflow-x-auto"><?php echo e($connectionTest['response_body']); ?></pre>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Troubleshooting Guide -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Troubleshooting Guide</h3>
            
            <div class="space-y-4">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h4 class="font-medium text-gray-900">Store Under Construction</h4>
                    <p class="text-gray-600 text-sm mt-1">
                        The store at <code><?php echo e($config['store_url'] ?? 'N/A'); ?></code> shows "Pardon our dust!" message, 
                        indicating it's under construction or in maintenance mode.
                    </p>
                </div>

                <div class="border-l-4 border-yellow-500 pl-4">
                    <h4 class="font-medium text-gray-900">API Keys May Need Regeneration</h4>
                    <p class="text-gray-600 text-sm mt-1">
                        If you're getting 401 errors, the API keys might be expired or have insufficient permissions.
                        Generate new keys in WooCommerce → Settings → Advanced → REST API.
                    </p>
                </div>

                <div class="border-l-4 border-green-500 pl-4">
                    <h4 class="font-medium text-gray-900">Application Still Works</h4>
                    <p class="text-gray-600 text-sm mt-1">
                        The application automatically falls back to mock data when the API is unavailable, 
                        so users can still browse products and test functionality.
                    </p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <a href="<?php echo e(route('admin.fix-api')); ?>"
                   class="bg-red-600 text-white px-4 py-3 rounded-md hover:bg-red-700 text-center font-semibold">
                    🔧 Fix API Keys
                </a>

                <a href="<?php echo e(route('test.woocommerce')); ?>"
                   class="bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 text-center">
                    Full API Test
                </a>

                <a href="<?php echo e(route('admin.diagnose-api')); ?>"
                   class="bg-purple-600 text-white px-4 py-3 rounded-md hover:bg-purple-700 text-center">
                    Detailed Diagnosis
                </a>

                <a href="<?php echo e(route('home')); ?>"
                   class="bg-gray-600 text-white px-4 py-3 rounded-md hover:bg-gray-700 text-center">
                    View Live Site
                </a>
            </div>
        </div>

        <!-- Current Application Status -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-3">Application Status</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="font-medium text-blue-800">✅ Working Features:</h4>
                    <ul class="text-blue-700 mt-2 space-y-1">
                        <li>• Homepage with featured products</li>
                        <li>• Product browsing (mock data)</li>
                        <li>• Shopping cart functionality</li>
                        <li>• Wishlist system</li>
                        <li>• Checkout process</li>
                        <li>• Payment methods (demo)</li>
                        <li>• User authentication</li>
                        <li>• Order tracking</li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-medium text-blue-800">🔄 Using Mock Data:</h4>
                    <ul class="text-blue-700 mt-2 space-y-1">
                        <li>• Product listings</li>
                        <li>• Product categories</li>
                        <li>• Product details</li>
                        <li>• Search functionality</li>
                        <li>• Featured products</li>
                        <li>• Sale items</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/admin/api-status.blade.php ENDPATH**/ ?>