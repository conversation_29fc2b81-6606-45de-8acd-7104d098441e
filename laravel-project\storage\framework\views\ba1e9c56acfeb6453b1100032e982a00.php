<?php $__env->startSection('title', 'Product Categories'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Product Categories</h1>
            <p class="text-gray-600 mt-2">Browse our wide range of product categories</p>
        </div>

        <?php if(empty($categories)): ?>
            <div class="bg-white rounded-lg shadow-md p-8 text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-6">
                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                
                <h2 class="text-xl font-semibold text-gray-900 mb-2">No categories available</h2>
                <p class="text-gray-600 mb-6">Categories will appear here once they are loaded from WooCommerce</p>
                
                <a href="<?php echo e(route('shop.index')); ?>" 
                   class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Browse All Products
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                    <div class="relative">
                        <?php if(!empty($category['image']['src'])): ?>
                            <img src="<?php echo e($category['image']['src']); ?>" 
                                 alt="<?php echo e($category['name']); ?>" 
                                 class="w-full h-48 object-cover">
                        <?php else: ?>
                            <div class="w-full h-48 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                                <span class="text-white text-2xl font-bold"><?php echo e(substr($category['name'], 0, 1)); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if(isset($category['count']) && $category['count'] > 0): ?>
                            <div class="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                                <?php echo e($category['count']); ?> products
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($category['name']); ?></h3>
                        
                        <?php if(!empty($category['description'])): ?>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                <?php echo e(strip_tags($category['description'])); ?>

                            </p>
                        <?php endif; ?>
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">
                                <?php if(isset($category['count'])): ?>
                                    <?php echo e($category['count']); ?> <?php echo e($category['count'] === 1 ? 'product' : 'products'); ?>

                                <?php else: ?>
                                    View products
                                <?php endif; ?>
                            </span>
                            
                            <a href="<?php echo e(route('category.show', $category['slug'])); ?>" 
                               class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <!-- Popular Categories Section -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Popular Categories</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <?php
                $popularCategories = [
                    ['name' => 'Electronics', 'icon' => '📱', 'slug' => 'electronics'],
                    ['name' => 'Fashion', 'icon' => '👕', 'slug' => 'fashion'],
                    ['name' => 'Home & Garden', 'icon' => '🏠', 'slug' => 'home-garden'],
                    ['name' => 'Sports', 'icon' => '⚽', 'slug' => 'sports'],
                    ['name' => 'Books', 'icon' => '📚', 'slug' => 'books'],
                    ['name' => 'Beauty', 'icon' => '💄', 'slug' => 'beauty']
                ];
                ?>
                
                <?php $__currentLoopData = $popularCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('shop.index', ['category' => $category['slug']])); ?>" 
                   class="bg-white rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-200 border border-gray-200">
                    <div class="text-3xl mb-2"><?php echo e($category['icon']); ?></div>
                    <div class="text-sm font-medium text-gray-900"><?php echo e($category['name']); ?></div>
                </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Category Features -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Why Shop by Category?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                        <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Easy Discovery</h3>
                    <p class="text-gray-600">Find exactly what you're looking for by browsing organized categories</p>
                </div>
                
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Best Prices</h3>
                    <p class="text-gray-600">Compare products within categories to find the best deals</p>
                </div>
                
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mb-4">
                        <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Quality Products</h3>
                    <p class="text-gray-600">All products are carefully curated for quality and value</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/categories/index.blade.php ENDPATH**/ ?>