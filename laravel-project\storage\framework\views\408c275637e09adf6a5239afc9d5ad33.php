<?php $__env->startSection('title', 'Shop - Deal4u'); ?>
<?php $__env->startSection('description', 'Browse our extensive collection of premium products at unbeatable prices. Find exactly what you\'re looking for with our advanced search and filtering options.'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-gray-50 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Shop</h1>
                    <p class="mt-2 text-gray-600">Discover amazing products at unbeatable prices</p>
                </div>
                
                <!-- Search and Sort -->
                <div class="mt-4 md:mt-0 flex flex-col sm:flex-row gap-4">
                    <!-- Search Form -->
                    <form action="<?php echo e(route('shop.index')); ?>" method="GET" class="flex">
                        <input type="hidden" name="category" value="<?php echo e(request('category')); ?>">
                        <input type="hidden" name="min_price" value="<?php echo e(request('min_price')); ?>">
                        <input type="hidden" name="max_price" value="<?php echo e(request('max_price')); ?>">
                        <input type="hidden" name="sale" value="<?php echo e(request('sale')); ?>">
                        <input type="hidden" name="orderby" value="<?php echo e(request('orderby')); ?>">
                        
                        <div class="relative">
                            <input type="text" 
                                   name="search" 
                                   value="<?php echo e(request('search')); ?>" 
                                   placeholder="Search products..." 
                                   class="w-full md:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors">
                            Search
                        </button>
                    </form>
                    
                    <!-- Sort Dropdown -->
                    <form action="<?php echo e(route('shop.index')); ?>" method="GET" id="sortForm">
                        <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                        <input type="hidden" name="category" value="<?php echo e(request('category')); ?>">
                        <input type="hidden" name="min_price" value="<?php echo e(request('min_price')); ?>">
                        <input type="hidden" name="max_price" value="<?php echo e(request('max_price')); ?>">
                        <input type="hidden" name="sale" value="<?php echo e(request('sale')); ?>">
                        
                        <select name="orderby" onchange="document.getElementById('sortForm').submit()" 
                                class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Sort by</option>
                            <option value="date" <?php echo e(request('orderby') == 'date' ? 'selected' : ''); ?>>Newest</option>
                            <option value="popularity" <?php echo e(request('orderby') == 'popularity' ? 'selected' : ''); ?>>Popularity</option>
                            <option value="price" <?php echo e(request('orderby') == 'price' ? 'selected' : ''); ?>>Price: Low to High</option>
                            <option value="price-desc" <?php echo e(request('orderby') == 'price-desc' ? 'selected' : ''); ?>>Price: High to Low</option>
                            <option value="rating" <?php echo e(request('orderby') == 'rating' ? 'selected' : ''); ?>>Rating</option>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="lg:grid lg:grid-cols-4 lg:gap-8">
            <!-- Sidebar Filters -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm p-6 sticky top-24">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                    
                    <form action="<?php echo e(route('shop.index')); ?>" method="GET" id="filterForm">
                        <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                        <input type="hidden" name="orderby" value="<?php echo e(request('orderby')); ?>">
                        
                        <!-- Categories Filter -->
                        <?php if(count($categories) > 0): ?>
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Categories</h4>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="" 
                                           <?php echo e(!request('category') ? 'checked' : ''); ?>

                                           onchange="document.getElementById('filterForm').submit()"
                                           class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">All Categories</span>
                                </label>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="flex items-center">
                                    <input type="radio" name="category" value="<?php echo e($category['id']); ?>" 
                                           <?php echo e(request('category') == $category['id'] ? 'checked' : ''); ?>

                                           onchange="document.getElementById('filterForm').submit()"
                                           class="text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700"><?php echo e($category['name']); ?> (<?php echo e($category['count']); ?>)</span>
                                </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Price Range Filter -->
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Price Range</h4>
                            <div class="flex items-center space-x-2">
                                <input type="number" 
                                       name="min_price" 
                                       value="<?php echo e(request('min_price')); ?>" 
                                       placeholder="Min" 
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <span class="text-gray-500">-</span>
                                <input type="number" 
                                       name="max_price" 
                                       value="<?php echo e(request('max_price')); ?>" 
                                       placeholder="Max" 
                                       class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="submit" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors">
                                    Apply
                                </button>
                            </div>
                        </div>
                        
                        <!-- Sale Items Filter -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" 
                                       name="sale" 
                                       value="true" 
                                       <?php echo e(request('sale') == 'true' ? 'checked' : ''); ?>

                                       onchange="document.getElementById('filterForm').submit()"
                                       class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Sale Items Only</span>
                            </label>
                        </div>
                        
                        <!-- Clear Filters -->
                        <?php if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale', 'orderby'])): ?>
                        <div class="pt-4 border-t border-gray-200">
                            <a href="<?php echo e(route('shop.index')); ?>" 
                               class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                Clear All Filters
                            </a>
                        </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="lg:col-span-3 mt-8 lg:mt-0">
                <!-- Active Filters Display -->
                <?php if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale'])): ?>
                <div class="mb-6 flex flex-wrap gap-2">
                    <?php if(request('search')): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                            Search: "<?php echo e(request('search')); ?>"
                            <a href="<?php echo e(route('shop.index', array_filter(request()->except('search')))); ?>" class="ml-2 text-blue-600 hover:text-blue-800">×</a>
                        </span>
                    <?php endif; ?>
                    
                    <?php if(request('category')): ?>
                        <?php
                            $selectedCategory = collect($categories)->firstWhere('id', request('category'));
                        ?>
                        <?php if($selectedCategory): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                            Category: <?php echo e($selectedCategory['name']); ?>

                            <a href="<?php echo e(route('shop.index', array_filter(request()->except('category')))); ?>" class="ml-2 text-green-600 hover:text-green-800">×</a>
                        </span>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php if(request('min_price') || request('max_price')): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                            Price: $<?php echo e(request('min_price', '0')); ?> - $<?php echo e(request('max_price', '∞')); ?>

                            <a href="<?php echo e(route('shop.index', array_filter(request()->except(['min_price', 'max_price'])))); ?>" class="ml-2 text-purple-600 hover:text-purple-800">×</a>
                        </span>
                    <?php endif; ?>
                    
                    <?php if(request('sale') == 'true'): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-red-100 text-red-800">
                            Sale Items
                            <a href="<?php echo e(route('shop.index', array_filter(request()->except('sale')))); ?>" class="ml-2 text-red-600 hover:text-red-800">×</a>
                        </span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <!-- Products Count -->
                <div class="flex items-center justify-between mb-6">
                    <p class="text-sm text-gray-600">
                        Showing <?php echo e(count($products)); ?> products
                        <?php if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale'])): ?>
                            (filtered)
                        <?php endif; ?>
                    </p>
                </div>
                
                <!-- Products Grid -->
                <?php if(count($products) > 0): ?>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $__env->make('components.product-card', ['product' => $product], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    
                    <!-- Load More Button (if needed) -->
                    <?php if(count($products) >= 24): ?>
                    <div class="text-center mt-12">
                        <button onclick="loadMoreProducts()" 
                                class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            Load More Products
                            <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <?php endif; ?>
                <?php else: ?>
                    <!-- No Products Found -->
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">No products found</h3>
                        <p class="mt-2 text-gray-600">
                            <?php if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale'])): ?>
                                Try adjusting your filters or search terms.
                            <?php else: ?>
                                Please check back later for new products.
                            <?php endif; ?>
                        </p>
                        <?php if(request()->hasAny(['search', 'category', 'min_price', 'max_price', 'sale'])): ?>
                        <div class="mt-6">
                            <a href="<?php echo e(route('shop.index')); ?>" 
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Clear All Filters
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function loadMoreProducts() {
    // This would implement pagination/infinite scroll
    // For now, just show a message
    showNotification('Load more functionality would be implemented here', 'info');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/shop/index.blade.php ENDPATH**/ ?>