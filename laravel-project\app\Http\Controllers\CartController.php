<?php

namespace App\Http\Controllers;

use App\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class CartController extends Controller
{
    protected $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * Display the cart page
     */
    public function index(): View
    {
        $cartSummary = $this->cartService->getCartSummary();

        return view('cart.index', compact('cartSummary'));
    }

    /**
     * Add item to cart (AJAX)
     */
    public function add(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|integer',
            'quantity' => 'integer|min:1',
            'variation' => 'array'
        ]);

        $result = $this->cartService->addToCart(
            $request->get('product_id'),
            $request->get('quantity', 1),
            $request->get('variation', [])
        );

        return response()->json($result);
    }

    /**
     * Update cart item quantity (AJAX)
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|integer',
            'quantity' => 'required|integer|min:0'
        ]);

        $result = $this->cartService->updateQuantity(
            $request->get('product_id'),
            $request->get('quantity')
        );

        return response()->json($result);
    }

    /**
     * Remove item from cart (AJAX)
     */
    public function remove(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|integer'
        ]);

        $result = $this->cartService->removeFromCart($request->get('product_id'));

        return response()->json($result);
    }

    /**
     * Clear entire cart (AJAX)
     */
    public function clear(): JsonResponse
    {
        $result = $this->cartService->clearCart();

        return response()->json($result);
    }

    /**
     * Get cart summary (AJAX)
     */
    public function summary(): JsonResponse
    {
        $cartSummary = $this->cartService->getCartSummary();

        return response()->json([
            'success' => true,
            'cart' => $cartSummary
        ]);
    }

    /**
     * Get cart count (AJAX)
     */
    public function count(): JsonResponse
    {
        $count = $this->cartService->getCartCount();

        return response()->json([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Check if product is in cart (AJAX)
     */
    public function check(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|integer'
        ]);

        $isInCart = $this->cartService->isInCart($request->get('product_id'));
        $quantity = $this->cartService->getProductQuantity($request->get('product_id'));

        return response()->json([
            'success' => true,
            'in_cart' => $isInCart,
            'quantity' => $quantity
        ]);
    }

    /**
     * Display checkout page
     */
    public function checkout()
    {
        $cartSummary = $this->cartService->getCartSummary();

        if ($cartSummary['item_count'] === 0) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty');
        }

        return view('cart.checkout', compact('cartSummary'));
    }

    /**
     * Process checkout (simplified version)
     */
    public function processCheckout(Request $request): JsonResponse
    {
        $request->validate([
            'billing_first_name' => 'required|string|max:255',
            'billing_last_name' => 'required|string|max:255',
            'billing_email' => 'required|email|max:255',
            'billing_phone' => 'required|string|max:20',
            'billing_address_1' => 'required|string|max:255',
            'billing_city' => 'required|string|max:255',
            'billing_state' => 'required|string|max:255',
            'billing_postcode' => 'required|string|max:20',
            'billing_country' => 'required|string|max:2',
            'payment_method' => 'required|string|in:stripe,paypal,cod'
        ]);

        $cartSummary = $this->cartService->getCartSummary();

        if ($cartSummary['item_count'] === 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cart is empty'
            ]);
        }

        // Prepare order data for WooCommerce
        $orderData = [
            'payment_method' => $request->get('payment_method'),
            'payment_method_title' => ucfirst($request->get('payment_method')),
            'set_paid' => $request->get('payment_method') === 'cod' ? false : true,
            'billing' => [
                'first_name' => $request->get('billing_first_name'),
                'last_name' => $request->get('billing_last_name'),
                'email' => $request->get('billing_email'),
                'phone' => $request->get('billing_phone'),
                'address_1' => $request->get('billing_address_1'),
                'address_2' => $request->get('billing_address_2', ''),
                'city' => $request->get('billing_city'),
                'state' => $request->get('billing_state'),
                'postcode' => $request->get('billing_postcode'),
                'country' => $request->get('billing_country')
            ],
            'shipping' => [
                'first_name' => $request->get('shipping_first_name', $request->get('billing_first_name')),
                'last_name' => $request->get('shipping_last_name', $request->get('billing_last_name')),
                'address_1' => $request->get('shipping_address_1', $request->get('billing_address_1')),
                'address_2' => $request->get('shipping_address_2', $request->get('billing_address_2', '')),
                'city' => $request->get('shipping_city', $request->get('billing_city')),
                'state' => $request->get('shipping_state', $request->get('billing_state')),
                'postcode' => $request->get('shipping_postcode', $request->get('billing_postcode')),
                'country' => $request->get('shipping_country', $request->get('billing_country'))
            ],
            'line_items' => [],
            'total' => (string) $cartSummary['total']
        ];

        // Add cart items to order
        foreach ($cartSummary['items'] as $item) {
            $orderData['line_items'][] = [
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->product['price']
            ];
        }

        // Here you would integrate with payment processors
        // For now, we'll just clear the cart and return success
        $this->cartService->clearCart();

        return response()->json([
            'success' => true,
            'message' => 'Order placed successfully!',
            'order_id' => 'ORD-' . time(), // Mock order ID
            'redirect_url' => route('cart.success')
        ]);
    }

    /**
     * Display order success page
     */
    public function success(): View
    {
        return view('cart.success');
    }
}
