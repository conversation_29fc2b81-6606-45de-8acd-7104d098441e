@extends('layouts.app')

@section('title', 'Stripe Payment')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-md p-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Complete Your Payment</h1>
            
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h2 class="text-lg font-semibold text-blue-900 mb-2">Order Summary</h2>
                <p class="text-blue-800">Total: €{{ number_format($total, 2) }}</p>
            </div>
            
            <form action="{{ route('stripe.process') }}" method="POST" id="payment-form">
                @csrf
                
                <div class="mb-6">
                    <label for="card-element" class="block text-sm font-medium text-gray-700 mb-2">
                        Credit or Debit Card
                    </label>
                    <div id="card-element" class="p-3 border border-gray-300 rounded-md">
                        <!-- Stripe Elements will create form elements here -->
                    </div>
                    <div id="card-errors" role="alert" class="text-red-500 text-sm mt-2"></div>
                </div>
                
                <button type="submit" id="submit-button" 
                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium disabled:opacity-50">
                    <span id="button-text">Pay €{{ number_format($total, 2) }}</span>
                    <span id="spinner" class="hidden">Processing...</span>
                </button>
            </form>
            
            <div class="mt-6 text-center">
                <a href="{{ route('cart.checkout') }}" class="text-gray-600 hover:text-gray-800">
                    ← Back to Checkout
                </a>
            </div>
        </div>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
    // For demo purposes, we'll simulate Stripe integration
    document.getElementById('payment-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Simulate processing
        const submitButton = document.getElementById('submit-button');
        const buttonText = document.getElementById('button-text');
        const spinner = document.getElementById('spinner');
        
        submitButton.disabled = true;
        buttonText.classList.add('hidden');
        spinner.classList.remove('hidden');
        
        // Simulate API call delay
        setTimeout(() => {
            // Create a hidden form to submit the demo data
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("stripe.process") }}';
            
            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);
            
            // Add demo stripe token
            const stripeToken = document.createElement('input');
            stripeToken.type = 'hidden';
            stripeToken.name = 'stripeToken';
            stripeToken.value = 'tok_demo_' + Math.random().toString(36).substr(2, 9);
            form.appendChild(stripeToken);
            
            // Add demo email
            const stripeEmail = document.createElement('input');
            stripeEmail.type = 'hidden';
            stripeEmail.name = 'stripeEmail';
            stripeEmail.value = '<EMAIL>';
            form.appendChild(stripeEmail);
            
            document.body.appendChild(form);
            form.submit();
        }, 2000);
    });
</script>
@endsection
