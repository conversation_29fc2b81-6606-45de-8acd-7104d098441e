@extends('layouts.app')

@section('title', 'Product Categories')

@section('content')
<!-- Categories Header -->
<section class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div class="text-center">
            <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-4">
                <span class="text-xs font-semibold tracking-wider">🛍️ SHOP BY CATEGORY 🛍️</span>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                Product Categories
            </h1>
            <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                Browse our wide range of premium gaming consoles, electronics, and tech products
            </p>
        </div>
    </div>
</section>

<div class="min-h-screen bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        @if(empty($categories))
            <div class="bg-white rounded-xl shadow-lg p-12 text-center border border-gray-100">
                <div class="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 mb-8">
                    <svg class="h-10 w-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>

                <h2 class="text-2xl font-bold text-gray-900 mb-4">No categories available</h2>
                <p class="text-gray-600 mb-8 text-lg">Categories will appear here once they are loaded from WooCommerce</p>

                <a href="{{ route('shop.index') }}"
                   class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold px-8 py-4 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    Browse All Products
                </a>
            </div>
        @else
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                @foreach($categories as $category)
                <div class="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="relative overflow-hidden">
                        @if(!empty($category['image']))
                            <img src="{{ $category['image'] }}"
                                 alt="{{ $category['name'] }}"
                                 class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
                        @else
                            <div class="w-full h-56 bg-gradient-to-br from-blue-500 via-purple-500 to-blue-600 flex items-center justify-center relative overflow-hidden">
                                <!-- Background pattern -->
                                <div class="absolute inset-0 opacity-20">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
                                </div>
                                <div class="relative z-10 text-center">
                                    <span class="text-white text-4xl font-bold mb-2 block">{{ substr($category['name'], 0, 1) }}</span>
                                    <div class="text-white text-xs opacity-75">{{ $category['name'] }}</div>
                                </div>
                            </div>
                        @endif

                        @if(isset($category['count']) && $category['count'] > 0)
                            <div class="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                                {{ $category['count'] }} products
                            </div>
                        @endif

                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                    </div>
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">{{ $category['name'] }}</h3>

                        @if(!empty($category['description']))
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                                {{ strip_tags($category['description']) }}
                            </p>
                        @endif

                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <svg class="w-4 h-4 mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                @if(isset($category['count']))
                                    {{ $category['count'] }} {{ $category['count'] === 1 ? 'product' : 'products' }}
                                @else
                                    View products
                                @endif
                            </div>

                            <a href="{{ route('category.show', $category['slug']) }}"
                               class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-5 py-2 rounded-lg text-sm transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                Browse
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @endif

        <!-- Popular Categories Section -->
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Popular Categories</h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                @php
                $popularCategories = [
                    ['name' => 'Electronics', 'icon' => '📱', 'slug' => 'electronics'],
                    ['name' => 'Fashion', 'icon' => '👕', 'slug' => 'fashion'],
                    ['name' => 'Home & Garden', 'icon' => '🏠', 'slug' => 'home-garden'],
                    ['name' => 'Sports', 'icon' => '⚽', 'slug' => 'sports'],
                    ['name' => 'Books', 'icon' => '📚', 'slug' => 'books'],
                    ['name' => 'Beauty', 'icon' => '💄', 'slug' => 'beauty']
                ];
                @endphp
                
                @foreach($popularCategories as $category)
                <a href="{{ route('shop.index', ['category' => $category['slug']]) }}" 
                   class="bg-white rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-200 border border-gray-200">
                    <div class="text-3xl mb-2">{{ $category['icon'] }}</div>
                    <div class="text-sm font-medium text-gray-900">{{ $category['name'] }}</div>
                </a>
                @endforeach
            </div>
        </div>

        <!-- Category Features -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Why Shop by Category?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                        <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Easy Discovery</h3>
                    <p class="text-gray-600">Find exactly what you're looking for by browsing organized categories</p>
                </div>
                
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Best Prices</h3>
                    <p class="text-gray-600">Compare products within categories to find the best deals</p>
                </div>
                
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mb-4">
                        <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Quality Products</h3>
                    <p class="text-gray-600">All products are carefully curated for quality and value</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
