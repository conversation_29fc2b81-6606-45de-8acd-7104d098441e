<!-- Product Loading Skeleton -->
<div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 animate-pulse">
    <div class="relative overflow-hidden">
        <!-- Image Skeleton -->
        <div class="aspect-square bg-gray-200"></div>
    </div>
    
    <div class="p-5">
        <!-- Category Skeleton -->
        <div class="h-3 bg-gray-200 rounded w-1/3 mb-2"></div>
        
        <!-- Title Skeleton -->
        <div class="space-y-2 mb-3">
            <div class="h-4 bg-gray-200 rounded w-full"></div>
            <div class="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
        
        <!-- Rating Skeleton -->
        <div class="flex items-center gap-2 mb-3">
            <div class="flex gap-1">
                <?php for($i = 0; $i < 5; $i++): ?>
                    <div class="w-4 h-4 bg-gray-200 rounded"></div>
                <?php endfor; ?>
            </div>
            <div class="h-3 bg-gray-200 rounded w-12"></div>
        </div>
        
        <!-- Price Skeleton -->
        <div class="flex items-center gap-2 mb-4">
            <div class="h-6 bg-gray-200 rounded w-20"></div>
            <div class="h-4 bg-gray-200 rounded w-16"></div>
        </div>
        
        <!-- Button Skeleton -->
        <div class="h-10 bg-gray-200 rounded-lg w-full"></div>
    </div>
</div>

<style>
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.animate-pulse {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}
</style>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/components/product-skeleton.blade.php ENDPATH**/ ?>