<!-- Quick View Modal -->
<div id="quickViewModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="closeQuickView()"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <!-- Close button -->
                <div class="absolute top-4 right-4 z-10">
                    <button onclick="closeQuickView()" class="bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 transition-colors">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Loading state -->
                <div id="quickViewLoading" class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>

                <!-- Product content -->
                <div id="quickViewContent" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Product Image -->
                        <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                            <img id="quickViewImage" src="" alt="" class="w-full h-full object-cover">
                        </div>

                        <!-- Product Details -->
                        <div class="flex flex-col justify-between">
                            <div>
                                <!-- Category -->
                                <p id="quickViewCategory" class="text-sm text-blue-600 font-semibold uppercase tracking-wide mb-2"></p>
                                
                                <!-- Title -->
                                <h2 id="quickViewTitle" class="text-2xl font-bold text-gray-900 mb-4"></h2>
                                
                                <!-- Rating -->
                                <div id="quickViewRating" class="flex items-center mb-4">
                                    <div class="flex text-yellow-400">
                                        <!-- Stars will be populated by JavaScript -->
                                    </div>
                                    <span class="ml-2 text-sm text-gray-600"></span>
                                </div>
                                
                                <!-- Price -->
                                <div id="quickViewPrice" class="mb-6">
                                    <!-- Price will be populated by JavaScript -->
                                </div>
                                
                                <!-- Description -->
                                <div id="quickViewDescription" class="text-gray-600 mb-6">
                                    <!-- Description will be populated by JavaScript -->
                                </div>
                                
                                <!-- Stock Status -->
                                <div id="quickViewStock" class="mb-6">
                                    <!-- Stock status will be populated by JavaScript -->
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="space-y-3">
                                <button id="quickViewAddToCart" 
                                        class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg">
                                    Add to Cart
                                </button>
                                
                                <a id="quickViewFullDetails" href="#" 
                                   class="block w-full text-center bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-lg transition-colors">
                                    View Full Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Quick View functionality
function quickView(productId) {
    const modal = document.getElementById('quickViewModal');
    const loading = document.getElementById('quickViewLoading');
    const content = document.getElementById('quickViewContent');
    
    // Show modal and loading state
    modal.classList.remove('hidden');
    loading.classList.remove('hidden');
    content.classList.add('hidden');
    
    // Simulate API call to get product details
    // In a real application, you would fetch this data from your API
    setTimeout(() => {
        // Mock product data - replace with actual API call
        const mockProduct = {
            id: productId,
            name: 'ANBERNIC RG Slide Handheld Game Console',
            category: 'Gaming Consoles',
            image: '/placeholder.jpg',
            price: 299.99,
            regular_price: 399.99,
            rating: 4.5,
            reviews: 128,
            description: 'Experience retro gaming like never before with this powerful handheld console featuring a slide-out keyboard design.',
            in_stock: true,
            stock_quantity: 15
        };
        
        populateQuickView(mockProduct);
        
        // Hide loading, show content
        loading.classList.add('hidden');
        content.classList.remove('hidden');
    }, 1000);
}

function populateQuickView(product) {
    document.getElementById('quickViewImage').src = product.image;
    document.getElementById('quickViewImage').alt = product.name;
    document.getElementById('quickViewCategory').textContent = product.category;
    document.getElementById('quickViewTitle').textContent = product.name;
    
    // Rating
    const ratingContainer = document.getElementById('quickViewRating');
    const starsHtml = generateStars(product.rating);
    ratingContainer.innerHTML = `
        <div class="flex text-yellow-400">${starsHtml}</div>
        <span class="ml-2 text-sm text-gray-600">(${product.reviews} reviews)</span>
    `;
    
    // Price
    const priceContainer = document.getElementById('quickViewPrice');
    if (product.price < product.regular_price) {
        priceContainer.innerHTML = `
            <div class="flex items-center gap-2">
                <span class="text-2xl font-bold text-red-600">$${product.price}</span>
                <span class="text-lg text-gray-500 line-through">$${product.regular_price}</span>
                <span class="bg-red-100 text-red-800 text-xs font-bold px-2 py-1 rounded">SALE</span>
            </div>
        `;
    } else {
        priceContainer.innerHTML = `<span class="text-2xl font-bold text-gray-900">$${product.price}</span>`;
    }
    
    // Description
    document.getElementById('quickViewDescription').textContent = product.description;
    
    // Stock
    const stockContainer = document.getElementById('quickViewStock');
    if (product.in_stock) {
        stockContainer.innerHTML = `
            <div class="flex items-center text-green-600">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                In Stock (${product.stock_quantity} available)
            </div>
        `;
    } else {
        stockContainer.innerHTML = `
            <div class="flex items-center text-red-600">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                Out of Stock
            </div>
        `;
    }
    
    // Update buttons
    document.getElementById('quickViewAddToCart').onclick = () => addToCart(product.id);
    document.getElementById('quickViewFullDetails').href = `/shop/product/${product.id}`;
}

function generateStars(rating) {
    let starsHtml = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            starsHtml += '<svg class="w-5 h-5 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
        } else {
            starsHtml += '<svg class="w-5 h-5 text-gray-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
        }
    }
    return starsHtml;
}

function closeQuickView() {
    document.getElementById('quickViewModal').classList.add('hidden');
}

// Close modal on Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeQuickView();
    }
});
</script>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/components/quick-view-modal.blade.php ENDPATH**/ ?>