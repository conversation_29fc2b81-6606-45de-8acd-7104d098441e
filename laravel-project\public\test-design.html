<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test New Design - Deal4u</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes bounce-slow {
            0%, 100% {
                transform: translateY(0) rotate(12deg);
            }
            50% {
                transform: translateY(-10px) rotate(12deg);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(12deg);
            }
            50% {
                transform: translateY(-20px) rotate(12deg);
            }
        }

        @keyframes float-delayed {
            0%, 100% {
                transform: translateY(0px) rotate(-6deg);
            }
            50% {
                transform: translateY(-15px) rotate(-6deg);
            }
        }

        .animate-bounce-slow {
            animation: bounce-slow 3s ease-in-out infinite;
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        .animate-float-delayed {
            animation: float-delayed 6s ease-in-out infinite 2s;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Hero Section with Sale Banner -->
    <section class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white relative overflow-hidden">
        <!-- NEW SALE RIBBON - Prominent at the top -->
        <div class="absolute top-0 left-0 right-0 bg-yellow-400 text-purple-900 py-2 px-4 text-center font-bold transform -skew-y-2 shadow-lg z-30">
            <div class="container mx-auto flex items-center justify-center gap-4">
                <span class="animate-pulse">🔥</span>
                <span class="text-lg uppercase tracking-wider">SUMMER SALE: UP TO 50% OFF EVERYTHING</span>
                <span class="animate-pulse">🔥</span>
            </div>
        </div>
        
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24 relative z-10">
            <!-- Promotional Banner - Floating -->
            <div class="absolute top-4 right-4 md:top-8 md:right-8 animate-bounce-slow">
                <div class="relative w-24 h-24 md:w-32 md:h-32 bg-yellow-400 rounded-full flex items-center justify-center transform rotate-12 shadow-xl">
                    <div class="text-center px-2">
                        <p class="text-xl md:text-2xl font-black text-purple-900 leading-none">50%</p>
                        <p class="text-sm md:text-base font-bold text-purple-900 leading-none">OFF</p>
                        <p class="text-[10px] md:text-xs mt-1 font-semibold text-purple-800">SUMMER50</p>
                    </div>
                </div>
            </div>

            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-2/3 text-center md:text-left">
                    <div class="inline-block px-3 py-1 bg-white bg-opacity-20 backdrop-blur-sm rounded-full mb-6 animate-pulse">
                        <span class="text-xs font-semibold tracking-wider">🔥 SUMMER SALE NOW LIVE 🔥</span>
                    </div>
                    
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Premium Products,
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                            Amazing Deals
                        </span>
                    </h1>
                    
                    <p class="text-lg md:text-xl mb-8 text-blue-100 max-w-2xl">
                        Discover thousands of high-quality gaming consoles, electronics, and tech products at unbeatable prices. Fast shipping, quality guarantee, and exceptional customer service.
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <a href="#" 
                           class="group bg-yellow-400 hover:bg-yellow-300 text-purple-900 font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center">
                            Shop Now
                            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                        <a href="#" 
                           class="group border-2 border-white hover:bg-white hover:text-purple-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center">
                            Browse Categories
                            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </a>
                    </div>
                    
                    <!-- Trust indicators -->
                    <div class="flex flex-wrap items-center justify-center md:justify-start gap-6 text-sm text-blue-200">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            <span>4.9/5 Rating</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>10,000+ Happy Customers</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <span>Free Shipping</span>
                        </div>
                    </div>
                </div>
                
                <!-- Hero Image/Visual -->
                <div class="md:w-1/3 mt-8 md:mt-0 relative">
                    <div class="relative w-full max-w-md mx-auto">
                        <!-- Floating product cards -->
                        <div class="absolute -top-4 -left-4 w-24 h-32 bg-white rounded-lg shadow-xl transform rotate-12 animate-float">
                            <div class="p-2">
                                <div class="w-full h-16 bg-gray-200 rounded mb-2"></div>
                                <div class="h-2 bg-gray-200 rounded mb-1"></div>
                                <div class="h-2 bg-gray-200 rounded w-2/3"></div>
                            </div>
                        </div>
                        <div class="absolute -top-2 -right-6 w-28 h-36 bg-white rounded-lg shadow-xl transform -rotate-6 animate-float-delayed">
                            <div class="p-2">
                                <div class="w-full h-20 bg-gray-200 rounded mb-2"></div>
                                <div class="h-2 bg-gray-200 rounded mb-1"></div>
                                <div class="h-2 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        </div>
                        
                        <!-- Main visual -->
                        <div class="relative z-10 bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 text-center">
                            <div class="text-6xl mb-4">🎮</div>
                            <h3 class="text-xl font-bold mb-2">Gaming Consoles</h3>
                            <p class="text-blue-200 text-sm">Latest models available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Test message -->
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded m-4">
        <strong>✅ Design Test Successful!</strong> The new "New folder (2)" design is working correctly.
    </div>
</body>
</html>
