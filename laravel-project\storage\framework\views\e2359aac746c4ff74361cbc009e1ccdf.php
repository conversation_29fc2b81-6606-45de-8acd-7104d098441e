<?php $__env->startSection('title', 'Fix WooCommerce API Keys'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Fix WooCommerce API Connection</h1>
            <p class="text-gray-600 mt-2">Step-by-step guide to generate new API keys and fix the 401 error</p>
        </div>

        <!-- Current Issue -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h2 class="text-lg font-semibold text-red-900 mb-3">🚨 Current Issue</h2>
            <div class="text-red-800">
                <p class="mb-2"><strong>Error:</strong> 401 - "woocommerce_rest_cannot_view"</p>
                <p class="mb-2"><strong>Cause:</strong> API keys don't have proper permissions or are invalid</p>
                <p><strong>Solution:</strong> Generate new API keys with correct permissions</p>
            </div>
        </div>

        <!-- Step-by-Step Guide -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">📋 Step-by-Step Fix</h2>
            
            <div class="space-y-6">
                <!-- Step 1 -->
                <div class="border-l-4 border-blue-500 pl-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 1: Access WooCommerce Settings</h3>
                    <p class="text-gray-600 mb-3">Go to your WordPress admin panel and navigate to WooCommerce API settings:</p>
                    <div class="bg-gray-100 p-3 rounded-md">
                        <p class="font-mono text-sm"><?php echo e($admin_url); ?></p>
                        <a href="<?php echo e($admin_url); ?>" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                            → Open WooCommerce API Settings
                        </a>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="border-l-4 border-green-500 pl-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 2: Create New API Key</h3>
                    <div class="space-y-2 text-gray-600">
                        <p>1. Click <strong>"Add Key"</strong> button</p>
                        <p>2. Fill in the form:</p>
                        <ul class="ml-4 space-y-1">
                            <li>• <strong>Description:</strong> "Laravel App API Key"</li>
                            <li>• <strong>User:</strong> Select an Administrator user</li>
                            <li>• <strong>Permissions:</strong> Select <span class="bg-yellow-200 px-2 py-1 rounded font-semibold">Read/Write</span></li>
                        </ul>
                        <p>3. Click <strong>"Generate API Key"</strong></p>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="border-l-4 border-purple-500 pl-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 3: Copy the New Keys</h3>
                    <p class="text-gray-600 mb-3">After generating, you'll see two keys. Copy them immediately (they won't be shown again):</p>
                    <div class="bg-gray-100 p-3 rounded-md space-y-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Consumer Key:</label>
                            <code class="text-xs text-gray-600">ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</code>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Consumer Secret:</label>
                            <code class="text-xs text-gray-600">cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</code>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="border-l-4 border-orange-500 pl-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Step 4: Test New Keys</h3>
                    <p class="text-gray-600 mb-3">Before updating your .env file, test the new keys here:</p>
                    
                    <form id="test-keys-form" class="space-y-4">
                        <?php echo csrf_field(); ?>
                        <div>
                            <label for="consumer_key" class="block text-sm font-medium text-gray-700 mb-1">Consumer Key</label>
                            <input type="text" id="consumer_key" name="consumer_key" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="ck_...">
                        </div>
                        
                        <div>
                            <label for="consumer_secret" class="block text-sm font-medium text-gray-700 mb-1">Consumer Secret</label>
                            <input type="text" id="consumer_secret" name="consumer_secret" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="cs_...">
                        </div>
                        
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                            Test New Keys
                        </button>
                    </form>
                    
                    <div id="test-results" class="mt-4 hidden"></div>
                </div>
            </div>
        </div>

        <!-- Current Configuration -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">🔧 Current Configuration</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Store Details</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Store URL:</span>
                            <span class="font-mono"><?php echo e($store_url); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">API Version:</span>
                            <span class="font-mono">wc/v3</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Current API Keys</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Consumer Key:</span>
                            <span class="font-mono"><?php echo e(substr($current_key, 0, 10)); ?>...</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Consumer Secret:</span>
                            <span class="font-mono"><?php echo e(substr($current_secret, 0, 10)); ?>...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final Steps -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-green-900 mb-3">✅ Final Steps (After Testing)</h2>
            <div class="text-green-800 space-y-2">
                <p>1. <strong>Update .env file</strong> with the new working keys</p>
                <p>2. <strong>Clear configuration cache:</strong> <code class="bg-green-100 px-2 py-1 rounded">php artisan config:clear</code></p>
                <p>3. <strong>Test the application</strong> - products should load from WooCommerce</p>
                <p>4. <strong>Check API status:</strong> <a href="<?php echo e(route('admin.api-status')); ?>" class="text-green-600 hover:text-green-800">View API Status</a></p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 flex flex-wrap gap-4">
            <a href="<?php echo e(route('admin.api-status')); ?>" 
               class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700">
                Check API Status
            </a>
            
            <a href="<?php echo e(route('test.woocommerce')); ?>" 
               class="bg-green-600 text-white px-6 py-3 rounded-md hover:bg-green-700">
                Full API Test
            </a>
            
            <a href="/admin/diagnose-api" 
               class="bg-purple-600 text-white px-6 py-3 rounded-md hover:bg-purple-700">
                Detailed Diagnosis
            </a>
        </div>
    </div>
</div>

<script>
document.getElementById('test-keys-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const resultsDiv = document.getElementById('test-results');
    
    // Show loading
    resultsDiv.innerHTML = '<div class="bg-blue-50 border border-blue-200 rounded-md p-4"><p class="text-blue-800">Testing new keys...</p></div>';
    resultsDiv.classList.remove('hidden');
    
    fetch('/admin/test-keys', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultsDiv.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-md p-4">
                    <h4 class="text-green-900 font-semibold mb-2">✅ Success! New keys work perfectly!</h4>
                    <p class="text-green-800 mb-3">${data.message}</p>
                    <div class="text-sm text-green-700">
                        <p><strong>Next steps:</strong></p>
                        <ul class="list-disc ml-4 mt-1">
                            ${data.next_steps.map(step => `<li>${step}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <h4 class="text-red-900 font-semibold mb-2">❌ Keys still have issues</h4>
                    <p class="text-red-800 mb-2">${data.message}</p>
                    <pre class="text-xs text-red-700 bg-red-100 p-2 rounded overflow-x-auto">${JSON.stringify(data.error, null, 2)}</pre>
                </div>
            `;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <h4 class="text-red-900 font-semibold mb-2">❌ Error testing keys</h4>
                <p class="text-red-800">${error.message}</p>
            </div>
        `;
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\New folder (2)\laravel-project\resources\views/admin/generate-keys.blade.php ENDPATH**/ ?>